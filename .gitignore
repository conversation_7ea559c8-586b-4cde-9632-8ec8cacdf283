# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
node_modules
/.pnp
.pnp.js
/serverless.yml
/.serverless


MIGRATION_GUIDE.md


# testing
/coverage

# next.js
/.next/
/out/


/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env
dist
# verceli
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
