import mongoose from "mongoose";
import User from "../models/user.model.js";
import Plan from "../models/plan.model.js";
import Product from "../models/product.model.js";
import TechStack from "../models/techstack.model.js";
import Folder from "../models/folder.model.js";
import File from "../models/file.model.js";

describe("Model Tests", () => {
  beforeAll(async () => {
    // Connect to test database
    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(process.env.MONGODB_URI || "mongodb://localhost:27017/designbyte_test");
    }
  });

  afterAll(async () => {
    // Clean up and close connection
    await mongoose.connection.close();
  });

  describe("Plan Model", () => {
    test("Should create plan with JSON benefits", async () => {
      const planData = {
        name: "Test Pro Plan",
        price: 29.99,
        currency: "USD",
        duration: "monthly",
        benefits: {
          templates: 100,
          support: "priority",
          features: ["analytics", "custom_domain"],
          storage: "unlimited"
        }
      };

      const plan = new Plan(planData);
      expect(plan.benefits).toEqual(planData.benefits);
      expect(typeof plan.benefits).toBe("object");
    });
  });

  describe("Product Model", () => {
    test("Should create product with TechStack references and JSON fields", async () => {
      // Create a test tech stack first
      const techStack = new TechStack({
        name: "React",
        slug: "react",
        description: "A JavaScript library for building user interfaces"
      });

      const productData = {
        name: "Test Template",
        fullName: "Test React Template",
        slug: "test-react-template",
        type: "templates",
        techStack: [techStack._id],
        keyFeatures: {
          responsive: true,
          darkMode: true,
          components: ["header", "footer", "sidebar"]
        },
        highlights: {
          performance: "Optimized for speed",
          accessibility: "WCAG compliant",
          seo: "SEO friendly"
        }
      };

      const product = new Product(productData);
      expect(product.techStack).toEqual([techStack._id]);
      expect(typeof product.keyFeatures).toBe("object");
      expect(typeof product.highlights).toBe("object");
    });
  });

  describe("Folder and File Models", () => {
    test("Should create folder with proper hierarchy", async () => {
      const folderData = {
        name: "Test Folder",
        slug: "test-folder",
        createdBy: new mongoose.Types.ObjectId()
      };

      const folder = new Folder(folderData);
      expect(folder.name).toBe("Test Folder");
      expect(folder.slug).toBe("test-folder");
      expect(folder.level).toBe(0);
      expect(folder.path).toBe("/Test Folder");
    });

    test("Should create file with proper folder reference", async () => {
      const folderId = new mongoose.Types.ObjectId();
      const userId = new mongoose.Types.ObjectId();

      const fileData = {
        name: "test-image.jpg",
        originalName: "test-image.jpg",
        folder: folderId,
        url: "https://example.com/test-image.jpg",
        fileSize: 1024000,
        mimeType: "image/jpeg",
        createdBy: userId,
        uploadedBy: userId
      };

      const file = new File(fileData);
      expect(file.folder).toEqual(folderId);
      expect(file.fileType).toBe("image"); // Should be auto-determined from mimeType
      expect(file.mimeType).toBe("image/jpeg");
    });

    test("Should auto-determine file type from MIME type", async () => {
      const testCases = [
        { mimeType: "image/png", expectedType: "image" },
        { mimeType: "video/mp4", expectedType: "video" },
        { mimeType: "audio/mpeg", expectedType: "audio" },
        { mimeType: "application/pdf", expectedType: "document" },
        { mimeType: "application/zip", expectedType: "archive" },
        { mimeType: "application/javascript", expectedType: "code" },
        { mimeType: "application/unknown", expectedType: "other" }
      ];

      for (const testCase of testCases) {
        const file = new File({
          name: "test-file",
          originalName: "test-file",
          folder: new mongoose.Types.ObjectId(),
          url: "https://example.com/test-file",
          fileSize: 1024,
          mimeType: testCase.mimeType,
          createdBy: new mongoose.Types.ObjectId(),
          uploadedBy: new mongoose.Types.ObjectId()
        });

        // Trigger the pre-save middleware
        await file.validate();
        expect(file.fileType).toBe(testCase.expectedType);
      }
    });
  });

  describe("Model Relationships", () => {
    test("Should properly reference related models", () => {
      // Test that models have proper references
      const productSchema = Product.schema;
      const techStackField = productSchema.paths.techStack;
      
      expect(techStackField).toBeDefined();
      expect(techStackField.schema.paths[0].options.ref).toBe("TechStack");

      const fileSchema = File.schema;
      const folderField = fileSchema.paths.folder;
      
      expect(folderField).toBeDefined();
      expect(folderField.options.ref).toBe("Folder");
    });
  });
});

// Export for potential use in other test files
export {
  User,
  Plan,
  Product,
  TechStack,
  Folder,
  File
};
