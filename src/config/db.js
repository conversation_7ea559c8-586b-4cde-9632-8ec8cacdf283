// import mongoose from "mongoose";

// const connectDB = async () => {
//   try {
//     const conn = await mongoose.connect(process.env.MONGODB_URI);
//     // const conn = await mongoose.connect(process.env.MONGODB_URI, {
//     //   useNewUrlParser: true,
//     //   useUnifiedTopology: true,
//     // });
//     console.log(`MongoDB Connected: ${conn.connection.host}`);
//   } catch (error) {
//     console.error(`Database Connection Error: ${error.message}`);
//     process.exit(1);
//   }
// };

// export default connectDB;

// 2nd way

// import mongoose from "mongoose";
// import logger from "../utils/logger.js"; // Ensure you have a logger utility or use console.log for simplicity

// const connectDB = async () => {
//   try {
//     mongoose.set("strictQuery", false);

//     const conn = await mongoose.connect(process.env.MONGODB_URI, {
//       useNewUrlParser: true,
//       useUnifiedTopology: true,
//     });

//     logger.info(`MongoDB connected: ${conn.connection.host}`);
//   } catch (error) {
//     logger.error("MongoDB connection error:", error.stack || error);
//     throw new Error("Failed to connect to MongoDB");
//   }

//   // Event listeners for the connection
//   const db = mongoose.connection;
//   db.on("error", (err) => logger.error(`MongoDB error: ${err.message}`));
// };

// export default connectDB;

// 3rd way

import mongoose from "mongoose";
import logger from "../utils/logger.js";

// Helper function to manage database connection
const connectDB = async () => {
  if (mongoose.connection.readyState === 1) {
    // Connection is already established
    logger.info("MongoDB connection is already active.");
    return;
  }

  try {
    mongoose.set("strictQuery", false);

    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    logger.info(`MongoDB connected successfully: ${mongoose.connection.host}`);
  } catch (error) {
    logger.error("Failed to connect to MongoDB:", error.stack || error);
    throw new Error("Database connection error.");
  }
};

// Database health check controller
export const checkDBHealth = async (req, res) => {
  const dbState = mongoose.connection.readyState;

  /**
   * MongoDB Connection States:
   * 0 = disconnected
   * 1 = connected
   * 2 = connecting
   * 3 = disconnecting
   */
  const status = {
    0: "Disconnected",
    1: "Connected",
    2: "Connecting",
    3: "Disconnecting",
  };

  try {
    // Check the connection state
    if (dbState === 1) {
      return res.status(200).json({
        status: "success",
        dbStatus: status[dbState],
        message: "Database connection is healthy.",
      });
    } else {
      return res.status(500).json({
        status: "fail",
        dbStatus: status[dbState],
        message: "Database is not connected properly.",
      });
    }
  } catch (error) {
    return res.status(500).json({
      status: "error",
      message: "An error occurred while checking the database connection.",
      error: error.message,
    });
  }
};

export default connectDB;
