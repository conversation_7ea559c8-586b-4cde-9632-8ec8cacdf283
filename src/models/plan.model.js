import mongoose from "mongoose";

const planSchema = new mongoose.Schema(
  {
    name: { type: String, required: true, unique: true }, // Name of the plan (e.g., "Basic", "Pro")
    description: { type: String }, // Description of the plan
    price: { type: Number, required: true }, // Price for the plan (in USD or your chosen currency)
    currency: { type: String, default: "USD" }, // Currency for the price
    duration: {
      type: String,
      enum: ["monthly", "yearly", "lifetime"],
      default: "monthly",
    }, // Plan duration (monthly, yearly, lifetime)
    isActive: { type: Boolean, default: true }, // Whether the plan is active or not
    maxTemplates: { type: Number, default: 5 }, // Maximum number of templates a user can access (if relevant)
    maxUsers: { type: Number, default: 1 }, // Maximum number of users (if relevant, for team plans)
    // benefits: [{ type: String }], // Array of benefits that come with the plan (e.g., "Access to premium templates", "Priority support")
    benefits: { type: String }, // Array of benefits that come with the plan (e.g., "Access to premium templates", "Priority support")
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" }, // Reference to the admin who created the plan
  },
  { timestamps: true }
);

export default mongoose.model("Plan", planSchema);
