import mongoose from "mongoose";

const templateSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    slug: { type: String, required: true, unique: true },
    description: { type: String },
    categories: [{ type: mongoose.Schema.Types.ObjectId, ref: "Category" }],
    superCategory: { type: mongoose.Schema.Types.ObjectId, ref: "Category" }, // Direct reference
    tags: [{ type: String }],
    techStack: [{ type: mongoose.Schema.Types.ObjectId, ref: "TechStack" }],
    price: { type: Number, default: 0 },
    isPaid: { type: Boolean, default: true },
    downloads: { type: Number, default: 0 },
    previewUrl: { type: String },
    authorId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    }, // Link to the author
    rating: { type: Number, default: 0, min: 0, max: 5 }, // Rating of the template
    keyFeatures: { type: mongoose.Schema.Types.Mixed }, // JSON data for key features
    highlights: { type: mongoose.Schema.Types.Mixed }, // JSON data for highlights
    status: {
      type: String,
      enum: ["active", "inactive", "archived"],
      default: "active",
    },
    isFeatured: { type: Boolean, default: false },
    discount: { type: Number, default: 0 },
  },
  { timestamps: true }
);

export default mongoose.model("Template", templateSchema);
