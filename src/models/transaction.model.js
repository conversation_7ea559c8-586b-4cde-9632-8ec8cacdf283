import mongoose from "mongoose";
import { 
  TRANSACTION_TYPES, 
  TRANSACTION_STATUSES, 
  PAYMENT_METHODS, 
  DEFAULT_CURRENCY 
} from "../utils/constants.js";

const transactionSchema = new mongoose.Schema(
  {
    // Core transaction details
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    type: {
      type: String,
      enum: Object.values(TRANSACTION_TYPES),
      required: true,
    },
    status: {
      type: String,
      enum: Object.values(TRANSACTION_STATUSES),
      default: TRANSACTION_STATUSES.PENDING,
    },
    
    // Financial details
    amount: { 
      type: Number, 
      required: true,
      min: 0, // Allow $0 transactions for 100% coupons
    },
    currency: { 
      type: String, 
      required: true, 
      default: DEFAULT_CURRENCY 
    },
    originalAmount: {
      type: Number,
      required: true,
      min: 0,
    }, // Amount before coupon discount
    discountAmount: {
      type: Number,
      default: 0,
      min: 0,
    }, // Amount discounted by coupon
    
    // Payment details
    paymentMethod: {
      type: String,
      enum: Object.values(PAYMENT_METHODS),
      required: true,
    },
    paymentGateway: {
      type: String,
      enum: ["stripe", "razorpay", "paypal", "manual"],
    },
    gatewayTransactionId: { 
      type: String,
      sparse: true, // Allow null but ensure uniqueness when present
    },
    gatewayResponse: {
      type: Object, // Store gateway-specific response data
    },
    
    // Plan and coupon references
    plan: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Plan",
      required: true,
    },
    coupon: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Coupon",
    },
    
    // Transaction metadata
    description: {
      type: String,
      required: false,
    },
    internalNotes: {
      type: String, // For admin notes
    },
    
    // Refund details
    refund: {
      isRefunded: { type: Boolean, default: false },
      refundAmount: { type: Number, default: 0 },
      refundReason: String,
      refundDate: Date,
      refundTransactionId: String,
      refundedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
    },
    
    // Dates
    processedAt: Date,
    failedAt: Date,
    expiresAt: Date, // For pending transactions
    
    // Related transactions
    parentTransaction: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Transaction",
    }, // For refunds, upgrades, etc.
    childTransactions: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: "Transaction",
    }], // For partial refunds, etc.
    
    // Simplified metadata field for all additional data
    metadata: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    },
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for better performance
transactionSchema.index({ user: 1, createdAt: -1 });
transactionSchema.index({ status: 1, createdAt: -1 });
transactionSchema.index({ gatewayTransactionId: 1 }, { sparse: true });
transactionSchema.index({ type: 1, status: 1 });
transactionSchema.index({ plan: 1, createdAt: -1 });

// Virtual for checking if transaction is free (100% coupon)
transactionSchema.virtual('isFree').get(function() {
  return this.amount === 0 && this.originalAmount > 0;
});

// Virtual for effective discount percentage
transactionSchema.virtual('discountPercentage').get(function() {
  if (this.originalAmount === 0) return 0;
  return Math.round((this.discountAmount / this.originalAmount) * 100);
});

// Pre-save middleware to set description if not provided
transactionSchema.pre('save', function(next) {
  if (!this.description && this.plan) {
    const action = this.type.replace('_', ' ').toLowerCase();
    this.description = `${action} - Plan ${this.plan}`;
  }

  // Set processedAt when status changes to completed
  if (this.status === TRANSACTION_STATUSES.COMPLETED && !this.processedAt) {
    this.processedAt = new Date();
  }

  // Set failedAt when status changes to failed
  if (this.status === TRANSACTION_STATUSES.FAILED && !this.failedAt) {
    this.failedAt = new Date();
  }

  next();
});

// Static method to create subscription transaction
transactionSchema.statics.createSubscriptionTransaction = function(data) {
  const {
    user,
    plan,
    coupon = null,
    paymentMethod,
    paymentGateway,
    type = TRANSACTION_TYPES.SUBSCRIPTION_PURCHASE,
    metadata = {}
  } = data;

  let amount = plan.price;
  let discountAmount = 0;
  let originalAmount = plan.price;

  // Apply coupon discount
  if (coupon) {
    if (coupon.discountType === 'percentage') {
      discountAmount = (originalAmount * coupon.discountValue) / 100;
    } else if (coupon.discountType === 'fixed') {
      discountAmount = Math.min(coupon.discountValue, originalAmount);
    }
    amount = Math.max(0, originalAmount - discountAmount);
  }

  // Store additional data in metadata
  const transactionMetadata = {
    ...metadata,
    planSnapshot: {
      name: plan.name,
      price: plan.price,
      duration: plan.duration,
      benefits: plan.benefits,
    },
    couponSnapshot: coupon ? {
      code: coupon.code,
      discountType: coupon.discountType,
      discountValue: coupon.discountValue,
    } : null
  };

  return new this({
    user,
    plan: plan._id,
    coupon: coupon?._id,
    type,
    amount,
    originalAmount,
    discountAmount,
    currency: plan.currency || DEFAULT_CURRENCY,
    paymentMethod: amount === 0 ? PAYMENT_METHODS.FREE : paymentMethod,
    paymentGateway,
    metadata: transactionMetadata,
    description: `${type.replace('_', ' ')} - ${plan.name} plan`,
  });
};

export default mongoose.model("Transaction", transactionSchema);
