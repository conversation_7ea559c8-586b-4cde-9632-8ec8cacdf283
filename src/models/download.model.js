import mongoose from "mongoose";

const downloadSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    productId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Product",
      required: true,
    },
    downloadType: {
      type: String,
      enum: ["github_redirect", "secure_download", "preview", "documentation"],
      required: true,
    },
    status: {
      type: String,
      enum: ["initiated", "completed", "failed", "expired"],
      default: "initiated",
    },
    
    // Download session tracking
    sessionId: { type: String }, // Unique session for this download
    downloadToken: { type: String }, // Temporary token for secure downloads
    tokenExpiresAt: { type: Date },
    
    // File information
    fileName: { type: String },
    fileSize: { type: Number }, // In bytes
    fileType: { type: String },
    
    // Request metadata
    ipAddress: { type: String },
    userAgent: { type: String },
    referrer: { type: String },
    
    // Timing information
    initiatedAt: { type: Date, default: Date.now },
    completedAt: { type: Date },
    
    // Error tracking
    errorMessage: { type: String },
    retryCount: { type: Number, default: 0 },
    
    // Analytics data
    downloadSource: {
      type: String,
      enum: ["web", "api", "mobile", "desktop"],
      default: "web",
    },
    
    // Subscription context
    subscriptionTier: {
      type: String,
      enum: ["free", "monthly", "yearly", "lifetime"],
    },
    accessLevel: {
      type: String,
      enum: ["free", "basic", "premium", "enterprise"],
    },
  },
  { timestamps: true }
);

// Indexes for efficient queries
downloadSchema.index({ userId: 1, createdAt: -1 });
downloadSchema.index({ productId: 1, createdAt: -1 });
downloadSchema.index({ downloadToken: 1 });
downloadSchema.index({ tokenExpiresAt: 1 });
downloadSchema.index({ status: 1, createdAt: -1 });

// Method to mark download as completed
downloadSchema.methods.markCompleted = function() {
  this.status = "completed";
  this.completedAt = new Date();
  return this.save();
};

// Method to mark download as failed
downloadSchema.methods.markFailed = function(errorMessage) {
  this.status = "failed";
  this.errorMessage = errorMessage;
  this.retryCount += 1;
  return this.save();
};

// Method to check if download token is valid
downloadSchema.methods.isTokenValid = function() {
  return this.downloadToken && 
         this.tokenExpiresAt && 
         this.tokenExpiresAt > new Date() &&
         this.status === "initiated";
};

// Static method to create download session
downloadSchema.statics.createDownloadSession = async function(userId, productId, downloadType, options = {}) {
  const sessionId = new mongoose.Types.ObjectId().toString();
  const downloadToken = Math.random().toString(36).substring(2, 15) + 
                       Math.random().toString(36).substring(2, 15);
  
  // Token expires in 1 hour by default
  const tokenExpiresAt = new Date(Date.now() + (options.tokenExpiryMinutes || 60) * 60 * 1000);
  
  return await this.create({
    userId,
    productId,
    downloadType,
    sessionId,
    downloadToken,
    tokenExpiresAt,
    ipAddress: options.ipAddress,
    userAgent: options.userAgent,
    referrer: options.referrer,
    downloadSource: options.downloadSource || "web",
    subscriptionTier: options.subscriptionTier,
    accessLevel: options.accessLevel,
    fileName: options.fileName,
    fileSize: options.fileSize,
    fileType: options.fileType,
  });
};

// Static method to get user download stats
downloadSchema.statics.getUserDownloadStats = async function(userId, timeframe = "month") {
  const startDate = new Date();
  
  switch (timeframe) {
    case "day":
      startDate.setDate(startDate.getDate() - 1);
      break;
    case "week":
      startDate.setDate(startDate.getDate() - 7);
      break;
    case "month":
      startDate.setMonth(startDate.getMonth() - 1);
      break;
    case "year":
      startDate.setFullYear(startDate.getFullYear() - 1);
      break;
  }
  
  return await this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        createdAt: { $gte: startDate },
        status: "completed"
      }
    },
    {
      $group: {
        _id: null,
        totalDownloads: { $sum: 1 },
        totalSize: { $sum: "$fileSize" },
        uniqueProducts: { $addToSet: "$productId" }
      }
    },
    {
      $project: {
        _id: 0,
        totalDownloads: 1,
        totalSize: 1,
        uniqueProductsCount: { $size: "$uniqueProducts" }
      }
    }
  ]);
};

export default mongoose.model("Download", downloadSchema);
