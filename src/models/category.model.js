import mongoose from "mongoose";

const categorySchema = new mongoose.Schema(
  {
    name: { type: String, required: true, unique: true },
    slug: { type: String, unique: true, required: true }, // For SEO-friendly URLs
    type: { type: String, required: true, default: "product" }, // Category type (e.g., "product", "blog")
    description: { type: String },
    isActive: { type: Boolean, default: true },
    parent: { type: mongoose.Schema.Types.ObjectId, ref: "Category" }, // For subcategories
  },
  { timestamps: true }
);

export default mongoose.model("Category", categorySchema);
