import mongoose from "mongoose";

const techStackSchema = new mongoose.Schema(
  {
    name: { type: String, required: true, unique: true }, // e.g., "React", "Node.js"
    slug: { type: String, required: true, unique: true }, // SEO-friendly identifier
    description: { type: String }, // Optional: Detailed description
    version: { type: String }, // Optional: e.g., "v17.0.2"
    image: { type: String }, // URL or path to the image for this tech stack
    isActive: { type: Boolean, default: true }, // To enable soft deletion or status toggling
  },
  { timestamps: true }
);

export default mongoose.model("TechStack", techStackSchema);
