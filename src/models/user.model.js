import mongoose from "mongoose";
import { ROLES } from "../utils/constants.js";

const userSchema = new mongoose.Schema(
  {
    // Authentication Details
    email: { type: String, required: true, unique: true },
    username: { type: String, unique: true, required: true },
    password: { type: String }, // For email auth
    googleId: { type: String, unique: true, sparse: true }, // For Google OAuth
    provider: {
      type: String,
      enum: ["google", "email"],
      default: "email",
    },

    // Profile Details
    firstName: { type: String },
    lastName: { type: String },
    profileImage: { type: String },
    phoneNumber: { type: String },

    // Role and Access
    role: { type: String, enum: Object.values(ROLES), default: ROLES.USER },
    isDeleted: { type: Boolean, default: false },

    // Status and Metadata
    lastLogin: { type: Date },
    isEmailVerified: { type: Boolean, default: false },
    emailVerificationToken: { type: String },
    passwordResetToken: { type: String },
    passwordResetExpires: { type: Date },

    // Simplified Subscription Fields
    isPro: { type: Boolean, default: false },
    isLifetimePro: { type: Boolean, default: false },
    subscriptionStartDate: { type: Date },
    subscriptionEndDate: { type: Date },

    // Current plan reference (we already have this, no need for currentPlanDetails)
    currentPlan: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Plan"
    },

    // Download tracking
    downloadCount: { type: Number, default: 0 },
    monthlyDownloadLimit: { type: Number, default: 5 }, // For free users
  },
  { timestamps: true }
);

// Method to check if user has access to a product
userSchema.methods.hasAccessToProduct = function(product) {
  // Free products are accessible to everyone
  if (!product.isPaid) return true;

  // Check if user has active subscription
  return this.hasActiveSubscription();
};

// Check if user has active subscription
userSchema.methods.hasActiveSubscription = function() {
  if (this.isLifetimePro) return true;
  return this.isPro && this.subscriptionEndDate && this.subscriptionEndDate > new Date();
};

// Check if user is lifetime pro
userSchema.methods.isLifetime = function() {
  return this.isLifetimePro;
};

// Check if subscription is expired
userSchema.methods.isSubscriptionExpired = function() {
  if (this.isLifetimePro) return false;
  if (!this.subscriptionEndDate) return true;
  return this.subscriptionEndDate < new Date();
};

// Activate subscription
userSchema.methods.activateSubscription = function(plan, startDate = new Date()) {
  this.isPro = true;
  this.subscriptionStartDate = startDate;
  this.currentPlan = plan._id;

  // Handle lifetime subscription
  if (plan.duration === "lifetime") {
    this.isLifetimePro = true;
    this.subscriptionEndDate = null;
  } else {
    // Calculate end date based on duration
    const endDate = new Date(startDate);
    if (plan.duration === "monthly") {
      endDate.setMonth(endDate.getMonth() + 1);
    } else if (plan.duration === "yearly") {
      endDate.setFullYear(endDate.getFullYear() + 1);
    }
    this.subscriptionEndDate = endDate;
  }

  return this.save();
};

// Cancel subscription
userSchema.methods.cancelSubscription = function() {
  this.isPro = false;
  this.isLifetimePro = false;
  this.subscriptionEndDate = null;
  this.currentPlan = null;
  return this.save();
};

// Renew subscription
userSchema.methods.renewSubscription = function(plan) {
  if (this.isLifetimePro) {
    throw new Error("Lifetime subscriptions cannot be renewed");
  }

  const currentEndDate = this.subscriptionEndDate || new Date();
  const newEndDate = new Date(currentEndDate);

  if (plan.duration === "monthly") {
    newEndDate.setMonth(newEndDate.getMonth() + 1);
  } else if (plan.duration === "yearly") {
    newEndDate.setFullYear(newEndDate.getFullYear() + 1);
  }

  this.subscriptionEndDate = newEndDate;
  this.isPro = true;
  this.currentPlan = plan._id;

  return this.save();
};

// Method to check download limits
userSchema.methods.canDownload = function() {
  if (this.hasActiveSubscription() || this.isLifetime()) {
    return true; // Pro users have unlimited downloads
  }
  return this.downloadCount < this.monthlyDownloadLimit;
};

// Method to increment download count
userSchema.methods.incrementDownload = function() {
  this.downloadCount += 1;
  return this.save();
};

// Reset monthly download count (to be called monthly for free users)
userSchema.methods.resetMonthlyDownloads = function() {
  if (!this.hasActiveSubscription()) {
    this.downloadCount = 0;
    return this.save();
  }
};

// Get subscription summary
userSchema.methods.getSubscriptionSummary = function() {
  return {
    isPro: this.isPro,
    isLifetime: this.isLifetimePro,
    startDate: this.subscriptionStartDate,
    endDate: this.subscriptionEndDate,
    hasActiveSubscription: this.hasActiveSubscription(),
    isExpired: this.isSubscriptionExpired(),
    currentPlan: this.currentPlan
  };
};

export default mongoose.model("User", userSchema);
