import mongoose from "mongoose";

const accessSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    productId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Product",
      required: true,
    },
    accessType: {
      type: String,
      enum: ["purchase", "subscription", "free", "admin_grant"],
      required: true,
    },
    grantedAt: {
      type: Date,
      default: Date.now,
    },
    expiresAt: {
      type: Date, // For time-limited access
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    downloadCount: {
      type: Number,
      default: 0,
    },
    lastAccessedAt: {
      type: Date,
    },
    // For tracking the source of access (subscription, one-time purchase, etc.)
    sourceSubscriptionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Subscription",
    },
    sourceOrderId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Order",
    },
    // Metadata for additional tracking
    metadata: {
      ipAddress: String,
      userAgent: String,
      downloadHistory: [{
        downloadedAt: { type: Date, default: Date.now },
        ipAddress: String,
        fileType: String,
      }],
    },
  },
  { timestamps: true }
);

// Compound index for efficient queries
accessSchema.index({ userId: 1, productId: 1 }, { unique: true });
accessSchema.index({ userId: 1, isActive: 1 });
accessSchema.index({ productId: 1, isActive: 1 });
accessSchema.index({ expiresAt: 1 });

// Method to check if access is still valid
accessSchema.methods.isValidAccess = function() {
  if (!this.isActive) return false;
  if (this.expiresAt && this.expiresAt < new Date()) return false;
  return true;
};

// Method to record a download
accessSchema.methods.recordDownload = function(metadata = {}) {
  this.downloadCount += 1;
  this.lastAccessedAt = new Date();
  
  if (metadata.ipAddress || metadata.fileType) {
    this.metadata.downloadHistory.push({
      downloadedAt: new Date(),
      ipAddress: metadata.ipAddress,
      fileType: metadata.fileType,
    });
  }
  
  return this.save();
};

// Static method to grant access to a user for a product
accessSchema.statics.grantAccess = async function(userId, productId, accessType, options = {}) {
  const existingAccess = await this.findOne({ userId, productId });
  
  if (existingAccess) {
    // Update existing access
    existingAccess.accessType = accessType;
    existingAccess.isActive = true;
    existingAccess.expiresAt = options.expiresAt;
    existingAccess.sourceSubscriptionId = options.sourceSubscriptionId;
    existingAccess.sourceOrderId = options.sourceOrderId;
    return await existingAccess.save();
  } else {
    // Create new access
    return await this.create({
      userId,
      productId,
      accessType,
      expiresAt: options.expiresAt,
      sourceSubscriptionId: options.sourceSubscriptionId,
      sourceOrderId: options.sourceOrderId,
    });
  }
};

// Static method to revoke access
accessSchema.statics.revokeAccess = async function(userId, productId) {
  return await this.findOneAndUpdate(
    { userId, productId },
    { isActive: false },
    { new: true }
  );
};

export default mongoose.model("Access", accessSchema);
