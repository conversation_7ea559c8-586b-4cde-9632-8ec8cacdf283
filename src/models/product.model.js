import mongoose from "mongoose";
import { PRODUCT_TYPES } from "../utils/constants.js";

const productSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    fullName: { type: String, required: true, unique: true },
    slug: { type: String, required: true, unique: true },
    description: { type: String },
    creator: { type: String },

    // Top-level type of product (e.g., template, starter-kit, notes, ebook)
    type: {
      type: String,
      enum: Object.values(PRODUCT_TYPES), // Use PRODUCT_TYPES for enum validation
      required: true,
    },
    // Subcategories related to the type (e.g., "portfolio", "eCommerce", "SaaS")
    subTypes: [{ type: String }],

    // in future i may move directly to reference
    category: { type: mongoose.Schema.Types.ObjectId, ref: "Category" },

    // Tech stack references for proper normalization
    techStack: [{ type: mongoose.Schema.Types.ObjectId, ref: "TechStack" }],

    // Tags for additional classification or search (e.g., ["web-development", "frontend"])
    tags: [{ type: String }],

    // images: [{ type: String }], // Product images
    images: { type: String },

    price: { type: Number, default: 0 },
    isPaid: { type: Boolean, default: true },
    isFeatured: { type: Boolean, default: false },
    discount: { type: Number, default: 0 },

    // Access Control and Subscription Tiers
    accessLevel: {
      type: String,
      enum: ["free", "basic", "premium", "enterprise"],
      default: "free",
    },
    requiredSubscription: {
      type: String,
      enum: ["none", "monthly", "yearly", "lifetime"],
      default: "none",
    },
    isExclusive: { type: Boolean, default: false }, // For premium/exclusive content

    // Content Delivery
    contentType: {
      type: String,
      enum: ["github", "direct_download", "secure_link", "external"],
      default: "github",
    },

    // Secure content storage (for paid products)
    secureContent: {
      downloadUrl: { type: String }, // Secure S3/storage URL
      fileSize: { type: Number }, // In bytes
      fileName: { type: String },
      fileType: { type: String }, // zip, pdf, etc.
      checksum: { type: String }, // For file integrity
    },

    // Resource Links: separate fields for each URL
    previewUrl: { type: String }, // URL for preview
    githubUrl: { type: String }, // URL for GitHub
    paymentLink: { type: String }, // URL for payment

    

    // Engagement Metrics
    downloads: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    rating: { type: Number, default: 0, min: 0, max: 5 },

    // Features and Highlights - JSON data for structured content
    keyFeatures: { type: mongoose.Schema.Types.Mixed }, // JSON array or object for features
    highlights: { type: mongoose.Schema.Types.Mixed }, // JSON array or object for highlights
    

    // Product Status (active, inactive, archived)
    status: {
      type: String,
      enum: ["active", "inactive", "archived"],
      default: "active",
    },
  },
  { timestamps: true }
);

// Indexing fields for improved query performance

// Index on `slug` (unique index is already created with `unique: true`)
productSchema.index({ slug: 1 });

// Index on `type` to quickly filter by product type (e.g., "template", "starter-kit")
productSchema.index({ type: 1 });

// Index on `status` for quickly filtering by product status (e.g., "active", "inactive", "archived")
productSchema.index({ status: 1 });

// Index on access level for subscription-based filtering
productSchema.index({ accessLevel: 1 });
productSchema.index({ requiredSubscription: 1 });
productSchema.index({ isPaid: 1, accessLevel: 1 });

// Method to check if product requires subscription
productSchema.methods.requiresSubscription = function() {
  return this.requiredSubscription !== "none" || this.accessLevel !== "free";
};

// Method to get content based on user access
productSchema.methods.getContentForUser = function(user) {
  // Free content - return GitHub URL
  if (!this.isPaid || this.accessLevel === "free") {
    return {
      type: "github",
      url: this.githubUrl,
      accessGranted: true,
    };
  }

  // Check if user has access
  if (!user || !user.hasAccessToProduct(this)) {
    return {
      type: "restricted",
      accessGranted: false,
      message: "Subscription required to access this content",
      requiredSubscription: this.requiredSubscription,
    };
  }

  // Return secure content for paid users
  return {
    type: "secure",
    accessGranted: true,
    contentType: this.contentType,
    secureContent: this.secureContent,
  };
};

export default mongoose.model("Product", productSchema);

// db.products.find({ type: 'template', subcategories: 'eCommerce' }).explain("executionStats");
