import mongoose from "mongoose";

// Unified File Management System
// This replaces the separate image, gallery, and folder models

const fileSystemSchema = new mongoose.Schema(
  {
    // Basic Information
    name: { type: String, required: true, trim: true },
    type: {
      type: String,
      enum: ["folder", "file"],
      required: true
    },

    // Hierarchy Management
    parent: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "FileSystem",
      default: null // null for root folders/files
    },
    path: { type: String, required: true }, // Full path for quick access

    // File-specific fields (only for type: "file")
    fileDetails: {
      // Storage information
      url: { type: String }, // File URL
      key: { type: String }, // Storage key (S3, etc.)
      fileName: { type: String }, // Original filename
      fileSize: { type: Number }, // Size in bytes
      mimeType: { type: String }, // MIME type

      // Image-specific metadata
      dimensions: {
        width: { type: Number },
        height: { type: Number }
      },

      // Source information
      source: {
        type: String,
        enum: ["S3", "UploadThing", "Unsplash", "External", "Local"],
        default: "S3"
      },
      sourceDetails: { type: String }, // Additional source metadata

      // File integrity
      checksum: { type: String }, // For file verification
    },

    // Content Management
    title: { type: String }, // Display title (can be different from name)
    description: { type: String }, // File/folder description
    tags: [{ type: String, trim: true }], // Tags for categorization
    category: { type: String }, // Category classification

    // Access Control
    isPublic: { type: Boolean, default: true },
    isDeleted: { type: Boolean, default: false }, // Soft delete
    isFeatured: { type: Boolean, default: false }, // Featured content

    // User Management
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User"
    }, // For files, who uploaded it

    // Statistics (mainly for files)
    views: { type: Number, default: 0 },
    downloads: { type: Number, default: 0 },

    // Additional metadata
    metadata: {
      takenAt: { type: Date }, // For photos with EXIF data
      exifData: { type: mongoose.Schema.Types.Mixed }, // Raw EXIF data
      customData: { type: mongoose.Schema.Types.Mixed }, // Any additional data
    }
  },
  {
    timestamps: true,
    // Add discriminator key for potential future extensions
    discriminatorKey: 'itemType'
  }
);

// Indexes for performance
fileSystemSchema.index({ parent: 1, name: 1 }); // Folder contents
fileSystemSchema.index({ path: 1 }, { unique: true }); // Path uniqueness
fileSystemSchema.index({ type: 1, isDeleted: 1 }); // Type filtering
fileSystemSchema.index({ createdBy: 1, type: 1 }); // User's items
fileSystemSchema.index({ tags: 1 }); // Tag-based searches
fileSystemSchema.index({ isPublic: 1, isFeatured: 1 }); // Public/featured content
fileSystemSchema.index({ "fileDetails.mimeType": 1 }); // File type filtering

// Virtual for getting full path
fileSystemSchema.virtual("fullPath").get(function() {
  return this.path;
});

// Virtual for checking if item is a folder
fileSystemSchema.virtual("isFolder").get(function() {
  return this.type === "folder";
});

// Virtual for checking if item is a file
fileSystemSchema.virtual("isFile").get(function() {
  return this.type === "file";
});

// Pre-save middleware to generate path
fileSystemSchema.pre("save", async function(next) {
  if (this.isNew || this.isModified("parent") || this.isModified("name")) {
    try {
      let pathParts = [this.name];
      let currentParent = this.parent;

      // Build path by traversing up the hierarchy
      while (currentParent) {
        const parentDoc = await this.constructor.findById(currentParent);
        if (!parentDoc) break;
        pathParts.unshift(parentDoc.name);
        currentParent = parentDoc.parent;
      }

      this.path = "/" + pathParts.join("/");
    } catch (error) {
      return next(error);
    }
  }
  next();
});

// Static method to find all children of a folder
fileSystemSchema.statics.findChildren = function(parentId, options = {}) {
  const query = {
    parent: parentId,
    isDeleted: false,
    ...options
  };
  return this.find(query).sort({ type: -1, name: 1 }); // Folders first, then files
};

// Static method to find files by type
fileSystemSchema.statics.findFilesByType = function(mimeTypePattern, options = {}) {
  const query = {
    type: "file",
    "fileDetails.mimeType": { $regex: mimeTypePattern, $options: "i" },
    isDeleted: false,
    ...options
  };
  return this.find(query);
};

// Instance method to get all descendants
fileSystemSchema.methods.getDescendants = async function() {
  const descendants = [];
  const queue = [this._id];

  while (queue.length > 0) {
    const currentId = queue.shift();
    const children = await this.constructor.find({
      parent: currentId,
      isDeleted: false
    });

    for (const child of children) {
      descendants.push(child);
      if (child.type === "folder") {
        queue.push(child._id);
      }
    }
  }

  return descendants;
};

// Instance method to soft delete with all descendants
fileSystemSchema.methods.softDelete = async function() {
  this.isDeleted = true;
  await this.save();

  if (this.type === "folder") {
    const descendants = await this.getDescendants();
    for (const descendant of descendants) {
      descendant.isDeleted = true;
      await descendant.save();
    }
  }
};

// Instance method to increment view count
fileSystemSchema.methods.incrementViews = function() {
  if (this.type === "file") {
    this.views += 1;
    return this.save();
  }
};

// Instance method to increment download count
fileSystemSchema.methods.incrementDownloads = function() {
  if (this.type === "file") {
    this.downloads += 1;
    return this.save();
  }
};

const FileSystem = mongoose.model("FileSystem", fileSystemSchema);

export default FileSystem;
