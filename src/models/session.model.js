import mongoose from "mongoose";

const sessionSchema = new mongoose.Schema(
  {
    userId: { 
      type: mongoose.Schema.Types.ObjectId, 
      ref: "User", 
      required: true 
    }, // Link to the user
    sessionId: { 
      type: String, 
      required: true, 
      unique: true 
    }, // Unique session identifier
    ipAddress: { type: String }, // IP address for tracking login origin (optional)
    userAgent: { type: String }, // Browser/User-Agent for tracking device (optional)
    createdAt: { 
      type: Date, 
      default: Date.now 
    }, // Timestamp for when the session was created
    lastActivity: { 
      type: Date, 
      default: Date.now 
    }, // Last activity timestamp (for expiry management)
  },
  { timestamps: true }
);

// Indexing to speed up queries
sessionSchema.index({ userId: 1, createdAt: -1 });

export default mongoose.model("Session", sessionSchema);
