import mongoose from "mongoose";

//  Metadata Schema
const metadataSchema = new mongoose.Schema(
  {
    name: { type: String, required: true, unique: true }, // e.g., "React", "Ecommerce"
    slug: { type: String, required: true, unique: true }, // SEO-friendly identifier
    description: { type: String }, // Optional: Provide more details about the metadata
    type: {
      type: String,
      required: true,
      //   enum: ["tag", "techStack", "category", "superCategory"],
      enum: Object.values(PRODUCT_TYPES),
    }, // Distinguishes between types
    isActive: { type: Boolean, default: true }, // Allow active or inactive state deletion
    isDeleted: { type: Boolean, default: false }, // Soft delete metadata
    
  },
  { timestamps: true }
);

// Add indexes for better querying performance
metadataSchema.index({ type: 1 });
metadataSchema.index({ slug: 1 });

const Metadata = mongoose.model("Metadata", metadataSchema);
