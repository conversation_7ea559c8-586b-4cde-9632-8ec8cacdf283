/**
 * @swagger
 * components:
 *   schemas:
 *     Product:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: Name of the product
 *         description:
 *           type: string
 *           description: Brief description of the product
 *         type:
 *           type: string
 *           description: Type of the product (template, theme, etc.)
 *           enum:
 *             - template
 *             - theme
 *             - starter_kit
 *             - plugin
 *             - resource
 *         categories:
 *           type: array
 *           items:
 *             type: string
 *           description: List of category IDs
 *         superCategory:
 *           type: string
 *           description: ID of the parent category
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *           description: Array of tags
 *         techStack:
 *           type: array
 *           items:
 *             type: string
 *           description: List of technologies related to the product
 *         price:
 *           type: number
 *           description: Price of the product
 *           default: 0
 *         isPaid:
 *           type: boolean
 *           description: Whether the product is paid or free
 *           default: true
 *         downloads:
 *           type: number
 *           description: Number of times the product has been downloaded
 *           default: 0
 *         previewUrl:
 *           type: string
 *           description: URL to preview the product
 *         paymentLink:
 *           type: object
 *           properties:
 *             platform:
 *               type: string
 *               description: Payment platform (e.g., Razorpay, Stripe, PayPal)
 *             link:
 *               type: string
 *               description: Payment link URL
 *         authorId:
 *           type: string
 *           description: ID of the author
 *         rating:
 *           type: number
 *           description: Rating of the product (0-5)
 *           default: 0
 *         keyFeatures:
 *           type: array
 *           items:
 *             type: string
 *           description: List of key features
 *         highlights:
 *           type: array
 *           items:
 *             type: string
 *           description: List of highlights
 *         status:
 *           type: string
 *           description: Status of the product
 *           enum:
 *             - active
 *             - inactive
 *             - archived
 *           default: active
 *         isFeatured:
 *           type: boolean
 *           description: Whether the product is featured
 *           default: false
 *         discount:
 *           type: number
 *           description: Discount on the product
 *           default: 0
 *   responses:
 *     ValidationError:
 *       description: Validation error
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               success:
 *                 type: boolean
 *                 example: false
 *               message:
 *                 type: string
 *                 example: Validation failed
 *               errors:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     field:
 *                       type: string
 *                       example: name
 *                     message:
 *                       type: string
 *                       example: Name is required
 */

/**
 * @swagger
 * /products:
 *   post:
 *     summary: Create a new product
 *     tags: [Products]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Product'
 *     responses:
 *       201:
 *         description: Product created successfully
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *   get:
 *     summary: Get all products
 *     tags: [Products]
 *     responses:
 *       200:
 *         description: List of products
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Product'
 *
 * /products/{id}:
 *   get:
 *     summary: Get a product by ID
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: The product ID
 *     responses:
 *       200:
 *         description: Product details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Product'
 *       404:
 *         description: Product not found
 *   put:
 *     summary: Update a product by ID
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: The product ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Product'
 *     responses:
 *       200:
 *         description: Product updated successfully
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *   delete:
 *     summary: Delete a product by ID
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: The product ID
 *     responses:
 *       204:
 *         description: Product deleted successfully
 *       404:
 *         description: Product not found
 */
