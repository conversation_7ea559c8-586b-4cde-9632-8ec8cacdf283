/**
 * @swagger
 * tags:
 *   name: Orders
 *   description: Order management
 */

/**
 * @swagger
 * /orders:
 *   post:
 *     summary: Create a new order
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user:
 *                 type: string
 *                 format: ObjectId
 *               templates:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     template:
 *                       type: string
 *                       format: ObjectId
 *                     price:
 *                       type: number
 *               totalAmount:
 *                 type: number
 *               payment:
 *                 type: string
 *                 format: ObjectId
 *               status:
 *                 type: string
 *                 enum: [completed, failed, pending]
 *                 default: pending
 *               deliveryEmail:
 *                 type: string
 *               shippingAddress:
 *                 type: object
 *                 properties:
 *                   street:
 *                     type: string
 *                   city:
 *                     type: string
 *                   state:
 *                     type: string
 *                   postalCode:
 *                     type: string
 *                   country:
 *                     type: string
 *             required:
 *               - user
 *               - templates
 *               - totalAmount
 *               - payment
 *     responses:
 *       201:
 *         description: Order created successfully
 *       400:
 *         description: Validation error
 */

/**
 * @swagger
 * /orders:
 *   get:
 *     summary: Get all orders
 *     tags: [Orders]
 *     responses:
 *       200:
 *         description: List of all orders
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   user:
 *                     type: string
 *                     format: ObjectId
 *                   templates:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         template:
 *                           type: string
 *                           format: ObjectId
 *                         price:
 *                           type: number
 *                   totalAmount:
 *                     type: number
 *                   payment:
 *                     type: string
 *                     format: ObjectId
 *                   status:
 *                     type: string
 *                   deliveryEmail:
 *                     type: string
 *                   shippingAddress:
 *                     type: object
 *                     properties:
 *                       street:
 *                         type: string
 *                       city:
 *                         type: string
 *                       state:
 *                         type: string
 *                       postalCode:
 *                         type: string
 *                       country:
 *                         type: string
 */

/**
 * @swagger
 * /orders/{id}:
 *   get:
 *     summary: Get an order by ID
 *     tags: [Orders]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: ObjectId
 *     responses:
 *       200:
 *         description: Order details
 *       404:
 *         description: Order not found
 */

/**
 * @swagger
 * /orders/{id}:
 *   put:
 *     summary: Update an order
 *     tags: [Orders]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: ObjectId
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               totalAmount:
 *                 type: number
 *               payment:
 *                 type: string
 *                 format: ObjectId
 *               status:
 *                 type: string
 *                 enum: [completed, failed, pending]
 *               deliveryEmail:
 *                 type: string
 *               shippingAddress:
 *                 type: object
 *                 properties:
 *                   street:
 *                     type: string
 *                   city:
 *                     type: string
 *                   state:
 *                     type: string
 *                   postalCode:
 *                     type: string
 *                   country:
 *                     type: string
 *     responses:
 *       200:
 *         description: Order updated successfully
 *       404:
 *         description: Order not found
 */

/**
 * @swagger
 * /orders/{id}:
 *   delete:
 *     summary: Delete an order
 *     tags: [Orders]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: ObjectId
 *     responses:
 *       200:
 *         description: Order deleted successfully
 *       404:
 *         description: Order not found
 */
