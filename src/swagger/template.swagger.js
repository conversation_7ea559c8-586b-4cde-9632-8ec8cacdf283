/**
 * @swagger
 * /templates:
 *   post:
 *     summary: Create a new template
 *     tags: [Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               categories:
 *                 type: array
 *                 items:
 *                   type: string
 *               superCategory:
 *                 type: string
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               price:
 *                 type: number
 *               isPaid:
 *                 type: boolean
 *               previewUrl:
 *                 type: string
 *               authorId:
 *                 type: string
 *               keyFeatures:
 *                 type: array
 *                 items:
 *                   type: string
 *               highlights:
 *                 type: array
 *                 items:
 *                   type: string
 *               status:
 *                 type: string
 *                 enum: [active, inactive]
 *               isFeatured:
 *                 type: boolean
 *               discount:
 *                 type: number
 *                 format: float
 *                 description: "Discount between 0 and 100"
 *     responses:
 *       201:
 *         description: Template created successfully
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /templates:
 *   get:
 *     summary: Get all templates
 *     tags: [Templates]
 *     responses:
 *       200:
 *         description: List of all templates
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                   description:
 *                     type: string
 *                   categories:
 *                     type: array
 *                     items:
 *                       type: string
 *                   superCategory:
 *                     type: string
 *                   tags:
 *                     type: array
 *                     items:
 *                       type: string
 *                   price:
 *                     type: number
 *                   isPaid:
 *                     type: boolean
 *                   previewUrl:
 *                     type: string
 *                   authorId:
 *                     type: string
 *                   keyFeatures:
 *                     type: array
 *                     items:
 *                       type: string
 *                   highlights:
 *                     type: array
 *                     items:
 *                       type: string
 *                   status:
 *                     type: string
 *                     enum: [active, inactive]
 *                   isFeatured:
 *                     type: boolean
 *                   discount:
 *                     type: number
 *                     format: float
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /templates/{id}:
 *   get:
 *     summary: Get template by ID
 *     tags: [Templates]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: Template ID
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Template found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 template:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     description:
 *                       type: string
 *                     categories:
 *                       type: array
 *                       items:
 *                         type: string
 *                     superCategory:
 *                       type: string
 *                     tags:
 *                       type: array
 *                       items:
 *                         type: string
 *                     price:
 *                       type: number
 *                     isPaid:
 *                       type: boolean
 *                     previewUrl:
 *                       type: string
 *                     authorId:
 *                       type: string
 *                     keyFeatures:
 *                       type: array
 *                       items:
 *                         type: string
 *                     highlights:
 *                       type: array
 *                       items:
 *                         type: string
 *                     status:
 *                       type: string
 *                       enum: [active, inactive]
 *                     isFeatured:
 *                       type: boolean
 *                     discount:
 *                       type: number
 *                       format: float
 *       404:
 *         description: Template not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /templates/{id}:
 *   put:
 *     summary: Update template by ID
 *     tags: [Templates]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: Template ID
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               categories:
 *                 type: array
 *                 items:
 *                   type: string
 *               superCategory:
 *                 type: string
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               price:
 *                 type: number
 *               isPaid:
 *                 type: boolean
 *               previewUrl:
 *                 type: string
 *               authorId:
 *                 type: string
 *               keyFeatures:
 *                 type: array
 *                 items:
 *                   type: string
 *               highlights:
 *                 type: array
 *                 items:
 *                   type: string
 *               status:
 *                 type: string
 *                 enum: [active, inactive]
 *               isFeatured:
 *                 type: boolean
 *               discount:
 *                 type: number
 *                 format: float
 *     responses:
 *       200:
 *         description: Template updated successfully
 *       404:
 *         description: Template not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /templates/{id}:
 *   delete:
 *     summary: Delete template by ID
 *     tags: [Templates]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: Template ID
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Template deleted successfully
 *       404:
 *         description: Template not found
 *       500:
 *         description: Internal server error
 */
