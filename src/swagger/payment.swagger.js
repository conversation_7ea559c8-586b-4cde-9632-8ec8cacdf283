/**
 * @swagger
 * tags:
 *   name: Payments
 *   description: Payment management
 */

/**
 * @swagger
 * /payments:
 *   post:
 *     summary: Create a new payment
 *     tags: [Payments]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *                 format: ObjectId
 *               amount:
 *                 type: number
 *               currency:
 *                 type: string
 *                 default: USD
 *               method:
 *                 type: string
 *                 enum: [credit_card, paypal, bank_transfer]
 *               transactionId:
 *                 type: string
 *               description:
 *                 type: string
 *             required:
 *               - userId
 *               - amount
 *               - currency
 *               - method
 *     responses:
 *       201:
 *         description: Payment created successfully
 *       400:
 *         description: Validation error
 */

/**
 * @swagger
 * /payments:
 *   get:
 *     summary: Get all payments
 *     tags: [Payments]
 *     responses:
 *       200:
 *         description: List of all payments
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   userId:
 *                     type: string
 *                     format: ObjectId
 *                   amount:
 *                     type: number
 *                   currency:
 *                     type: string
 *                   method:
 *                     type: string
 *                     enum: [credit_card, paypal, bank_transfer]
 *                   status:
 *                     type: string
 *                   transactionId:
 *                     type: string
 *                   description:
 *                     type: string
 *                   isRefunded:
 *                     type: boolean
 */

/**
 * @swagger
 * /payments/{id}:
 *   get:
 *     summary: Get a payment by ID
 *     tags: [Payments]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: ObjectId
 *     responses:
 *       200:
 *         description: Payment details
 *       404:
 *         description: Payment not found
 */

/**
 * @swagger
 * /payments/{id}:
 *   put:
 *     summary: Update a payment
 *     tags: [Payments]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: ObjectId
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               amount:
 *                 type: number
 *               currency:
 *                 type: string
 *               method:
 *                 type: string
 *                 enum: [credit_card, paypal, bank_transfer]
 *               status:
 *                 type: string
 *               transactionId:
 *                 type: string
 *               description:
 *                 type: string
 *     responses:
 *       200:
 *         description: Payment updated successfully
 *       404:
 *         description: Payment not found
 */

/**
 * @swagger
 * /payments/{id}:
 *   delete:
 *     summary: Delete a payment record
 *     tags: [Payments]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: ObjectId
 *     responses:
 *       200:
 *         description: Payment deleted successfully
 *       404:
 *         description: Payment not found
 */
