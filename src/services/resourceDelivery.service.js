import crypto from "crypto";
import jwt from "jsonwebtoken";
import Download from "../models/download.model.js";
import Access from "../models/access.model.js";
import User from "../models/user.model.js";
import Product from "../models/product.model.js";

class ResourceDeliveryService {
  /**
   * Generate secure download token for authorized users
   */
  static async generateSecureDownloadToken(userId, productId, options = {}) {
    try {
      const user = await User.findById(userId);
      const product = await Product.findById(productId);

      if (!user || !product) {
        throw new Error("User or product not found");
      }

      // Check if user has access to the product
      if (!user.hasAccessToProduct(product)) {
        throw new Error("Access denied");
      }

      // Create download session
      const downloadSession = await Download.createDownloadSession(
        userId,
        productId,
        "secure_download",
        {
          ipAddress: options.ipAddress,
          userAgent: options.userAgent,
          referrer: options.referrer,
          downloadSource: options.downloadSource,
          subscriptionTier: user.subscriptionTier,
          accessLevel: user.accessLevel,
          fileName: product.secureContent?.fileName,
          fileSize: product.secureContent?.fileSize,
          fileType: product.secureContent?.fileType,
          tokenExpiryMinutes: options.tokenExpiryMinutes || 60,
        }
      );

      // Generate JWT token with download session info
      const tokenPayload = {
        downloadId: downloadSession._id,
        userId: userId,
        productId: productId,
        sessionId: downloadSession.sessionId,
        downloadToken: downloadSession.downloadToken,
        exp: Math.floor(downloadSession.tokenExpiresAt.getTime() / 1000),
      };

      const jwtToken = jwt.sign(tokenPayload, process.env.JWT_SECRET);

      return {
        downloadToken: jwtToken,
        sessionId: downloadSession.sessionId,
        expiresAt: downloadSession.tokenExpiresAt,
        downloadUrl: `/api/secure-download/${downloadSession.downloadToken}`,
      };
    } catch (error) {
      console.error("Error generating secure download token:", error);
      throw error;
    }
  }

  /**
   * Validate download token and return download info
   */
  static async validateDownloadToken(token) {
    try {
      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // Find download session
      const downloadSession = await Download.findById(decoded.downloadId)
        .populate("userId")
        .populate("productId");

      if (!downloadSession) {
        throw new Error("Download session not found");
      }

      // Check if token is still valid
      if (!downloadSession.isTokenValid()) {
        throw new Error("Download token expired or invalid");
      }

      return {
        downloadSession,
        user: downloadSession.userId,
        product: downloadSession.productId,
      };
    } catch (error) {
      console.error("Error validating download token:", error);
      throw error;
    }
  }

  /**
   * Generate GitHub redirect URL for free content
   */
  static generateGitHubRedirectUrl(product, user) {
    if (!product.githubUrl) {
      throw new Error("GitHub URL not available for this product");
    }

    // For free content, direct GitHub redirect
    if (!product.isPaid || product.accessLevel === "free") {
      return {
        type: "github_redirect",
        url: product.githubUrl,
        requiresAuth: false,
      };
    }

    // For paid content, check access first
    if (!user.hasAccessToProduct(product)) {
      throw new Error("Access denied");
    }

    return {
      type: "github_redirect",
      url: product.githubUrl,
      requiresAuth: true,
    };
  }

  /**
   * Create secure presigned URL for file downloads (for cloud storage)
   */
  static async createPresignedUrl(product, downloadSession, expiryMinutes = 60) {
    try {
      // This is a placeholder for cloud storage integration
      // You would implement actual S3/GCS/Azure presigned URL generation here
      
      const baseUrl = process.env.SECURE_STORAGE_BASE_URL || "https://your-secure-storage.com";
      const filePath = product.secureContent?.downloadUrl;
      
      if (!filePath) {
        throw new Error("Secure download URL not configured for this product");
      }

      // Generate signature for URL security
      const timestamp = Date.now();
      const expiry = timestamp + (expiryMinutes * 60 * 1000);
      const signature = crypto
        .createHmac("sha256", process.env.SECURE_STORAGE_SECRET || "default-secret")
        .update(`${filePath}:${expiry}:${downloadSession.sessionId}`)
        .digest("hex");

      const presignedUrl = `${baseUrl}${filePath}?expires=${expiry}&signature=${signature}&session=${downloadSession.sessionId}`;

      return {
        url: presignedUrl,
        expiresAt: new Date(expiry),
        fileName: product.secureContent?.fileName,
        fileSize: product.secureContent?.fileSize,
        fileType: product.secureContent?.fileType,
      };
    } catch (error) {
      console.error("Error creating presigned URL:", error);
      throw error;
    }
  }

  /**
   * Track download completion and update analytics
   */
  static async trackDownloadCompletion(downloadSessionId, metadata = {}) {
    try {
      const downloadSession = await Download.findById(downloadSessionId);
      
      if (!downloadSession) {
        throw new Error("Download session not found");
      }

      // Mark download as completed
      await downloadSession.markCompleted();

      // Update user download count
      const user = await User.findById(downloadSession.userId);
      if (user) {
        await user.incrementDownload();
      }

      // Update access record
      await Access.findOneAndUpdate(
        {
          userId: downloadSession.userId,
          productId: downloadSession.productId,
        },
        {
          $inc: { downloadCount: 1 },
          lastAccessedAt: new Date(),
          $push: {
            "metadata.downloadHistory": {
              downloadedAt: new Date(),
              ipAddress: metadata.ipAddress,
              fileType: downloadSession.fileType,
            },
          },
        }
      );

      // Update product download count
      await Product.findByIdAndUpdate(
        downloadSession.productId,
        { $inc: { downloads: 1 } }
      );

      return {
        success: true,
        downloadSession,
      };
    } catch (error) {
      console.error("Error tracking download completion:", error);
      throw error;
    }
  }

  /**
   * Get user's download history
   */
  static async getUserDownloadHistory(userId, options = {}) {
    try {
      const page = options.page || 1;
      const limit = Math.min(options.limit || 20, 100);
      const skip = (page - 1) * limit;

      const downloads = await Download.find({
        userId,
        status: "completed",
      })
        .populate("productId", "name slug type images")
        .sort({ completedAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Download.countDocuments({
        userId,
        status: "completed",
      });

      return {
        downloads,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error("Error getting user download history:", error);
      throw error;
    }
  }

  /**
   * Clean up expired download tokens
   */
  static async cleanupExpiredTokens() {
    try {
      const result = await Download.deleteMany({
        tokenExpiresAt: { $lt: new Date() },
        status: { $in: ["initiated", "failed"] },
      });

      console.log(`Cleaned up ${result.deletedCount} expired download tokens`);
      return result;
    } catch (error) {
      console.error("Error cleaning up expired tokens:", error);
      throw error;
    }
  }
}

export default ResourceDeliveryService;
