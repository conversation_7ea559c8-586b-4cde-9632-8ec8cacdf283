#!/usr/bin/env node

import dotenv from "dotenv";
import mongoose from "mongoose";
import Plan from "../models/plan.model.js";
import Product from "../models/product.model.js";
import TechStack from "../models/techstack.model.js";
import Folder from "../models/folder.model.js";
import File from "../models/file.model.js";

// Load environment variables
dotenv.config();

async function testModels() {
  try {
    console.log("🧪 Testing Model Changes...");
    console.log("=" .repeat(50));

    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("✅ Connected to database");

    // Test 1: Plan model with JSON benefits
    console.log("\n📋 Testing Plan Model...");
    const testPlan = new Plan({
      name: "Test Pro Plan",
      price: 29.99,
      currency: "USD",
      duration: "monthly",
      benefits: {
        templates: 100,
        support: "priority",
        features: ["analytics", "custom_domain"],
        storage: "unlimited"
      }
    });

    console.log("✅ Plan created with JSON benefits:", typeof testPlan.benefits);
    console.log("   Benefits:", JSON.stringify(testPlan.benefits, null, 2));

    // Test 2: TechStack model
    console.log("\n🔧 Testing TechStack Model...");
    const testTechStack = new TechStack({
      name: "React",
      slug: "react",
      description: "A JavaScript library for building user interfaces"
    });

    console.log("✅ TechStack created:", testTechStack.name);

    // Test 3: Product model with references and JSON fields
    console.log("\n📦 Testing Product Model...");
    const testProduct = new Product({
      name: "Test Template",
      fullName: "Test React Template",
      slug: "test-react-template",
      type: "templates",
      techStack: [testTechStack._id],
      keyFeatures: {
        responsive: true,
        darkMode: true,
        components: ["header", "footer", "sidebar"]
      },
      highlights: {
        performance: "Optimized for speed",
        accessibility: "WCAG compliant",
        seo: "SEO friendly"
      }
    });

    console.log("✅ Product created with TechStack reference");
    console.log("   TechStack IDs:", testProduct.techStack);
    console.log("   KeyFeatures type:", typeof testProduct.keyFeatures);
    console.log("   Highlights type:", typeof testProduct.highlights);

    // Test 4: Folder model
    console.log("\n📁 Testing Folder Model...");
    const testFolder = new Folder({
      name: "Test Folder",
      slug: "test-folder",
      createdBy: new mongoose.Types.ObjectId()
    });

    console.log("✅ Folder created:", testFolder.name);
    console.log("   Path will be:", testFolder.path || "Generated on save");

    // Test 5: File model
    console.log("\n📄 Testing File Model...");
    const testFile = new File({
      name: "test-image.jpg",
      originalName: "test-image.jpg",
      folder: testFolder._id,
      url: "https://example.com/test-image.jpg",
      fileSize: 1024000,
      mimeType: "image/jpeg",
      createdBy: new mongoose.Types.ObjectId(),
      uploadedBy: new mongoose.Types.ObjectId()
    });

    console.log("✅ File created with folder reference");
    console.log("   Folder ID:", testFile.folder);
    console.log("   MIME type:", testFile.mimeType);
    console.log("   File type will be determined on save");

    // Test 6: Model relationships
    console.log("\n🔗 Testing Model Relationships...");
    
    // Check Product -> TechStack reference
    const productSchema = Product.schema;
    const techStackField = productSchema.paths.techStack;
    console.log("✅ Product.techStack references:", techStackField.schema.paths[0].options.ref);

    // Check File -> Folder reference
    const fileSchema = File.schema;
    const folderField = fileSchema.paths.folder;
    console.log("✅ File.folder references:", folderField.options.ref);

    console.log("\n🎉 All model tests passed!");
    console.log("=" .repeat(50));

  } catch (error) {
    console.error("❌ Test failed:", error.message);
    console.error(error.stack);
  } finally {
    await mongoose.connection.close();
    console.log("🔌 Database connection closed");
  }
}

// Run tests if this script is executed directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  testModels()
    .then(() => {
      console.log("✅ Model testing completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Model testing failed:", error);
      process.exit(1);
    });
}

export default testModels;
