// app.js
import express from "express";
import authRoutes from "./routes/auth.routes.js";
import userRoutes from "./routes/user.routes.js";
import planRoutes from "./routes/plan.routes.js";
import categoryRoutes from "./routes/category.routes.js";
import couponRoutes from "./routes/coupon.routes.js";
import transactionRoutes from "./routes/transaction.routes.js";
import templateRoutes from "./routes/template.routes.js";
import productRoutes from "./routes/product.routes.js";
import fileSystemRoutes from "./routes/fileSystem.routes.js";
import folderRoutes from "./routes/folder.routes.js";
import fileRoutes from "./routes/file.routes.js";
import webhookRoutes from "./routes/webhook.routes.js";
import healthRoutes from "./routes/health.routes.js";
import accessRoutes from "./routes/access.routes.js";
import { swaggerSpec, swaggerUi } from "./utils/swagger.js";
import { errorMiddleware } from "./middlewares/error.middleware.js";
import { protect } from "./middlewares/auth.middleware.js";
import cors from "cors";
import rateLimit from "express-rate-limit";
import cookieParser from "cookie-parser";
import passport from "./config/passport.js";
import { initializeWebhookHandlers, validateWebhookConfiguration } from "./services/webhooks/index.js";

// import dotenv from 'dotenv';
// import userRoutes from './routes/user.routes';

// dotenv.config();

const app = express();

// Initialize webhook handlers
initializeWebhookHandlers();
validateWebhookConfiguration();

app.use(cookieParser());

// Initialize Passport
app.use(passport.initialize());

// NODE_ENV
// console.log(process.env.NODE_ENV);
//setup cors based on noded env
if (process.env.NODE_ENV === "production") {
  console.log("production");  
  app.use(
    cors({
      origin: [process.env.CORS_ORIGIN, "https://designbyte.shop"],
      credentials: true,
    })
  );
} else {
  console.log("cors development");
  app.use(cors({ origin: true, credentials: true }));
}



// app.use(
//   cors({
//     origin: [
//       process.env.CORS_ORIGIN,
//       "https://designbyte.shop",
//       "http://localhost:3000",
//     ],
//     credentials: true,
//   })
// );

const limiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour in milliseconds
  max: 400, // 400 requests per hour
});

// app.use(limiter);
// app.use(cors());
app.use(express.json());
app.use(errorMiddleware);

app.use("/api/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerSpec));

app.use("/api/v1", healthRoutes);
app.use("/api/users", protect, userRoutes);
app.use("/api/auth", authRoutes);
app.use("/api/plans", planRoutes);
app.use("/api/categories", categoryRoutes);
app.use("/api/coupons", couponRoutes);
app.use("/api/transactions", protect, transactionRoutes);
app.use("/api/products", productRoutes);
app.use("/api/templates", templateRoutes);
app.use("/api/files", fileSystemRoutes); // Legacy route - consider deprecating
app.use("/api/folders", folderRoutes); // New dedicated folder routes
app.use("/api/file", fileRoutes); // New dedicated file routes
app.use("/api/webhooks", webhookRoutes);
app.use("/api/access", accessRoutes);

export default app;
