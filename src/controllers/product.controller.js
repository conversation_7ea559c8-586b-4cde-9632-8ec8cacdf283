import Product from "../models/product.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import { slugGenerator } from "../utils/slug-generator.js";

// Get all active products
export const getAllProducts = asyncHandler(async (req, res) => {
  const products = await Product.find({ status: "active" }).select(
    "name fullName techStack type images slug price isPaid previewUrl paymentLink keyFeatures highlights isFeatured discount"
  );
  res.status(200).json({ success: true, data: products });
});

// Get products with cursor pagination
export const getProductsCursor = asyncHandler(async (req, res) => {
  const limit = Math.min(50, Math.max(1, parseInt(req.query.limit) || 10));
  const cursor = req.query.cursor;

  const query = cursor
    ? { _id: { $gt: cursor }, status: "active" }
    : { status: "active" };

  const products = await Product.find(query)
    .select(
      "name fullName techStack type images slug price isPaid previewUrl paymentLink keyFeatures highlights isFeatured discount"
    )
    .limit(limit)
    .sort({ _id: 1 });

  const hasNextPage = products.length === limit;

  res.status(200).json({
    success: true,
    data: products,
    hasNextPage,
    nextCursor: hasNextPage ? products[products.length - 1]._id : null,
  });
});

// Get products by type
export const getProductsByType = asyncHandler(async (req, res) => {
  const products = await Product.find({
    type: req.params.type,
    status: "active",
  }).select(
    "name fullName techStack type images slug price isPaid previewUrl paymentLink keyFeatures highlights isFeatured discount"
  );

  res.status(200).json({ success: true, data: products });
});

// Get product by slug
export const getProductBySlug = asyncHandler(async (req, res) => {
  const product = await Product.findOne({
    slug: req.params.slug,
    status: "active",
  });

  if (!product) {
    return res
      .status(404)
      .json({ success: false, message: "Product not found" });
  }

  res.status(200).json({ success: true, data: product });
});

// Aggregate products (e.g., by type)
export const aggregateProducts = asyncHandler(async (req, res) => {
  const aggregation = await Product.aggregate([
    {
      $match: {
        status: "active",
      },
    },
    {
      $group: {
        _id: "$type",
        totalCount: { $sum: 1 },
        avgPrice: { $avg: "$price" },
      },
    },
  ]);

  res.status(200).json({ success: true, data: aggregation });
});

// Get subTypes with product count based on type
export const getsubTypesCountByCategory = asyncHandler(async (req, res) => {
  const { type } = req.params;

  const aggregation = await Product.aggregate([
    {
      $match: {
        status: "active",
        type, // Match products within the specified type
      },
    },
    {
      $unwind: "$subTypes", // Unwind subTypes array
    },
    {
      $group: {
        _id: "$subTypes", // Group by subcategory name
        productCount: { $sum: 1 }, // Count the number of products in each subcategory
      },
    },
    {
      $sort: { productCount: -1 }, // Optionally, sort by the count in descending order
    },
  ]);

  res.status(200).json({ success: true, data: aggregation });
});

// Get subcategories count by category (for futuer)
export const getSubCategoriesCount = asyncHandler(async (req, res) => {
  const aggregation = await Product.aggregate([
    {
      $match: { status: "active" },
    },
    {
      $group: {
        _id: "$category", // Group by category
        subTypes: { $addToSet: "$subTypes" }, // Get unique subtypes
      },
    },
    {
      $project: {
        _id: 1,
        subTypesCount: { $size: "$subTypes" }, // Count number of unique subtypes
        subTypes: 1, // Include subTypes for reference
      },
    },
    {
      $lookup: {
        from: "categories", // Assuming "Category" is the name of the collection
        localField: "_id",
        foreignField: "_id",
        as: "categoryDetails",
      },
    },
    {
      $unwind: {
        path: "$categoryDetails",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        categoryId: "$_id",
        subTypesCount: 1,
        subTypes: 1,
        categoryName: {
          $ifNull: [
            { $arrayElemAt: ["$categoryDetails.name", 0] },
            "Uncategorized",
          ],
        },
      },
    },
  ]);

  res.status(200).json({
    success: true,
    data: aggregation,
  });

  //   Key Points:
  // $match: Filters products to only include active products.
  // $group: Groups products by category and collects all unique subTypes (subcategories).
  // $project: Adds a field subTypesCount that counts the unique subtypes.
  // $lookup: Joins the categories collection to get category details.
  // $unwind: Flattens the results from the $lookup stage.
  // $ifNull: Ensures a fallback category name if no category is found.
});

//admin routes

// Create product
export const createProduct = asyncHandler(async (req, res) => {
  const {
    name,
    fullName,
    images,
    slug,
    description,
    type,
    subTypes,
    category,
    techStack,
    tags,
    price,
    isPaid,
    discount,
    previewUrl,
    githubUrl,
    paymentLink,
    authorId,
    keyFeatures,
    highlights,
    status,
    isFeatured,
  } = req.body;

  const generatedSlug = slug || slugGenerator(fullName);

  const product = new Product({
    name,
    fullName,
    slug: generatedSlug,
    images,
    description,
    type,
    subTypes,
    category,
    techStack,
    tags,
    price,
    isPaid,
    discount,
    previewUrl,
    githubUrl,
    paymentLink,
    authorId,
    keyFeatures,
    highlights,
    status,
    isFeatured,
  });

  const createdProduct = await product.save();

  res.status(201).json({
    success: true,
    data: createdProduct,
    message: "Product created successfully",
  });
});

// Update product
export const updateProduct = asyncHandler(async (req, res) => {
  const productId = req.params.id;
  const updatedProduct = await Product.findByIdAndUpdate(productId, req.body, {
    new: true,
  });

  if (!updatedProduct) {
    return res
      .status(404)
      .json({ success: false, message: "Product not found" });
  }

  res.status(200).json({
    success: true,
    data: updatedProduct,
    message: "Product updated successfully",
  });
});

// Delete product
export const deleteProduct = asyncHandler(async (req, res) => {
  const productId = req.params.id;

  const deletedProduct = await Product.findByIdAndDelete(productId);

  if (!deletedProduct) {
    return res
      .status(404)
      .json({ success: false, message: "Product not found" });
  }

  res.status(200).json({
    success: true,
    message: "Product deleted successfully",
  });
});

//Get product by ID
export const getProductById = asyncHandler(async (req, res) => {
  const product = await Product.findById(req.params.id);

  if (!product) {
    return res
      .status(404)
      .json({ success: false, message: "Product not found" });
  }

  res.status(200).json({ success: true, data: product });
});

// Get products with pagination
export const getProductsPagination = asyncHandler(async (req, res) => {
  const page = Math.max(1, parseInt(req.query.page) || 1);
  const limit = Math.min(50, Math.max(1, parseInt(req.query.limit) || 10));
  const skip = (page - 1) * limit;

  const total = await Product.countDocuments();
  const products = await Product.find().skip(skip).limit(limit);

  res.status(200).json({
    success: true,
    data: products,
    total,
    currentPage: page,
    totalPages: Math.ceil(total / limit),
  });
});
