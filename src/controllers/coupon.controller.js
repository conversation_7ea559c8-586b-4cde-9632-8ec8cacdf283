import Coupon from "../models/coupon.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";

/**
 * Create a new coupon
 */
export const createCoupon = asyncHandler(async (req, res) => {
  const {
    code,
    discountType,
    discountValue,
    maxRedemptions,
    validFrom,
    validUntil,
    isActive,
  } = req.body;

  const existingCoupon = await Coupon.findOne({ code });
  if (existingCoupon) {
    // res.status(400);
    // throw new Error("Coupon code already exists");
    return res
      .status(400)
      .json({ success: false, message: "Coupon code already exists" });
  }

  const newCoupon = new Coupon({
    code,
    discountType,
    discountValue,
    maxRedemptions,
    validFrom,
    validUntil,
    isActive,
  });

  const savedCoupon = await newCoupon.save();
  res.status(201).json({ success: true, data: savedCoupon });
});

/**
 * Get all coupons
 */
export const getCoupons = asyncHandler(async (req, res) => {
  const coupons = await Coupon.find();
  res.status(200).json({ success: true, data: coupons });
});

/**
 * Get all coupons with pagination
 */

export const getCouponsWithPagination = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;
  const total = await Coupon.countDocuments();
  const pages = Math.ceil(total / limit);
  const coupons = await Coupon.find().skip(skip).limit(limit);

  res.status(200).json({
    success: true,
    data: coupons,
    totalDocuments: total,
    currentPage: page,
    totalPages: pages,
  });
});

/**
 * Get a single coupon by ID
 */
export const getCouponById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const coupon = await Coupon.findById(id);

  if (!coupon) {
    res.status(404);
    throw new Error("Coupon not found");
  }

  res.status(200).json({ success: true, data: coupon });
});

/**
 * Update a coupon
 */
export const updateCoupon = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updates = req.body;

  const updatedCoupon = await Coupon.findByIdAndUpdate(id, updates, {
    new: true,
  });

  if (!updatedCoupon) {
    res.status(404);
    throw new Error("Coupon not found");
  }

  res.status(200).json({ success: true, data: updatedCoupon });
});

/**
 * Delete a coupon
 */
export const deleteCoupon = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const deletedCoupon = await Coupon.findByIdAndDelete(id);
  // const deletedCoupon = await Coupon.findByIdAndRemove(id);

  if (!deletedCoupon) {
    res.status(404);
    throw new Error("Coupon not found");
  }

  res
    .status(200)
    .json({ success: true, message: "Coupon deleted successfully" });
});
