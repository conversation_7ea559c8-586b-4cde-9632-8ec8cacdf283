import Folder from "../models/folder.model.js";
import File from "../models/file.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";

// Create a new folder
export const createFolder = asyncHandler(async (req, res) => {
  const { name, slug, parent, title, description, tags, category } = req.body;
  const userId = req.user.id;

  const newFolder = new Folder({
    name,
    slug: slug || name.toLowerCase().replace(/\s+/g, '-'),
    parent: parent || null,
    title,
    description,
    tags,
    category,
    createdBy: userId,
    isPublic: true,
  });

  const savedFolder = await newFolder.save();
  res.status(201).json({
    success: true,
    message: "Folder created successfully.",
    data: savedFolder,
  });
});

// Upload/create a new file
export const createFile = asyncHandler(async (req, res) => {
  const {
    name,
    originalName,
    folder,
    url,
    key,
    fileSize,
    mimeType,
    title,
    description,
    tags,
    category,
    isPublic,
    isFeatured,
    dimensions,
    source,
    sourceDetails,
    checksum,
  } = req.body;
  const userId = req.user.id;

  const newFile = new File({
    name,
    originalName: originalName || name,
    folder: folder,
    url,
    key,
    fileSize,
    mimeType,
    title,
    description,
    tags,
    category,
    isPublic: isPublic !== undefined ? isPublic : true,
    isFeatured: isFeatured || false,
    dimensions,
    source: source || "S3",
    sourceDetails,
    checksum,
    createdBy: userId,
    uploadedBy: userId,
  });

  const savedFile = await newFile.save();

  // Update folder statistics
  if (folder) {
    await Folder.findByIdAndUpdate(folder, {
      $inc: { totalFiles: 1, totalSize: fileSize || 0 }
    });
  }

  res.status(201).json({
    success: true,
    message: "File created successfully.",
    data: savedFile,
  });
});

// Get folder contents (files and subfolders)
export const getFolderContents = asyncHandler(async (req, res) => {
  const { folderId } = req.params;
  const { page = 1, limit = 20, type } = req.query;

  const skip = (page - 1) * limit;
  const parentId = folderId === "root" ? null : folderId;

  let folders = [];
  let files = [];
  let total = 0;

  if (!type || type === "folder") {
    const folderQuery = { parent: parentId, isDeleted: false };
    folders = await Folder.find(folderQuery)
      .skip(type === "folder" ? skip : 0)
      .limit(type === "folder" ? parseInt(limit) : parseInt(limit))
      .sort({ name: 1 });

    if (type === "folder") {
      total = await Folder.countDocuments(folderQuery);
    }
  }

  if (!type || type === "file") {
    const fileQuery = { folder: parentId, isDeleted: false };
    files = await File.find(fileQuery)
      .skip(type === "file" ? skip : 0)
      .limit(type === "file" ? parseInt(limit) : parseInt(limit))
      .sort({ name: 1 });

    if (type === "file") {
      total = await File.countDocuments(fileQuery);
    }
  }

  if (!type) {
    // Combined results
    const folderCount = await Folder.countDocuments({ parent: parentId, isDeleted: false });
    const fileCount = await File.countDocuments({ folder: parentId, isDeleted: false });
    total = folderCount + fileCount;
  }

  const items = [...folders, ...files];
  const totalPages = Math.ceil(total / limit);

  res.status(200).json({
    success: true,
    data: items,
    pagination: {
      currentPage: parseInt(page),
      totalPages,
      totalItems: total,
      itemsPerPage: parseInt(limit),
    },
  });
});

// Search files and folders
export const searchFileSystem = asyncHandler(async (req, res) => {
  const { query, type, tags, category, page = 1, limit = 20 } = req.query;
  const skip = (page - 1) * limit;

  const searchFilter = {
    isDeleted: false,
    $or: [
      { name: { $regex: query || "", $options: "i" } },
      { title: { $regex: query || "", $options: "i" } },
      { description: { $regex: query || "", $options: "i" } },
    ],
  };

  if (tags) {
    searchFilter.tags = { $in: tags.split(",") };
  }

  if (category) {
    searchFilter.category = category;
  }

  let results = [];
  let total = 0;

  if (!type || type === "folder") {
    const folders = await Folder.find(searchFilter)
      .skip(type === "folder" ? skip : 0)
      .limit(type === "folder" ? parseInt(limit) : parseInt(limit))
      .sort({ name: 1 });
    results = [...results, ...folders];

    if (type === "folder") {
      total = await Folder.countDocuments(searchFilter);
    }
  }

  if (!type || type === "file") {
    const files = await File.find(searchFilter)
      .skip(type === "file" ? skip : 0)
      .limit(type === "file" ? parseInt(limit) : parseInt(limit))
      .sort({ name: 1 });
    results = [...results, ...files];

    if (type === "file") {
      total = await File.countDocuments(searchFilter);
    }
  }

  if (!type) {
    const folderCount = await Folder.countDocuments(searchFilter);
    const fileCount = await File.countDocuments(searchFilter);
    total = folderCount + fileCount;
  }

  const totalPages = Math.ceil(total / limit);

  res.status(200).json({
    success: true,
    data: results,
    pagination: {
      currentPage: parseInt(page),
      totalPages,
      totalItems: total,
      itemsPerPage: parseInt(limit),
    },
  });
});

// Get item by ID
export const getItemById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Try to find as folder first, then as file
  let item = await Folder.findById(id);
  let itemType = "folder";

  if (!item) {
    item = await File.findById(id);
    itemType = "file";
  }

  if (!item || item.isDeleted) {
    return res.status(404).json({
      success: false,
      message: "Item not found."
    });
  }

  // Increment view count for files
  if (itemType === "file") {
    await item.incrementViews();
  }

  res.status(200).json({ success: true, data: { ...item.toObject(), itemType } });
});

// Update item
export const updateItem = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  // Remove fields that shouldn't be updated directly
  delete updateData.type;
  delete updateData.createdBy;
  delete updateData.path;

  const updatedItem = await FileSystem.findByIdAndUpdate(
    id,
    updateData,
    { new: true, runValidators: true }
  );

  if (!updatedItem || updatedItem.isDeleted) {
    return res.status(404).json({ 
      success: false, 
      message: "Item not found." 
    });
  }

  res.status(200).json({
    success: true,
    message: "Item updated successfully.",
    data: updatedItem,
  });
});

// Soft delete items
export const deleteItems = asyncHandler(async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      success: false,
      message: "Please provide an array of IDs to delete.",
    });
  }

  let deletedCount = 0;
  for (const id of ids) {
    const item = await FileSystem.findById(id);
    if (item && !item.isDeleted) {
      await item.softDelete();
      deletedCount++;
    }
  }

  res.status(200).json({
    success: true,
    message: `${deletedCount} item(s) deleted successfully.`,
  });
});

// Permanently delete items
export const permanentDeleteItems = asyncHandler(async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      success: false,
      message: "Please provide an array of IDs to delete.",
    });
  }

  const deletedItems = await FileSystem.deleteMany({ _id: { $in: ids } });

  res.status(200).json({
    success: true,
    message: `${deletedItems.deletedCount} item(s) permanently deleted.`,
  });
});

// Restore deleted items
export const restoreItems = asyncHandler(async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      success: false,
      message: "Please provide an array of IDs to restore.",
    });
  }

  const restoredItems = await FileSystem.updateMany(
    { _id: { $in: ids }, isDeleted: true },
    { isDeleted: false }
  );

  res.status(200).json({
    success: true,
    message: `${restoredItems.modifiedCount} item(s) restored successfully.`,
  });
});

// Get files by type (images, documents, etc.)
export const getFilesByType = asyncHandler(async (req, res) => {
  const { mimeType, page = 1, limit = 20 } = req.query;
  const skip = (page - 1) * limit;

  if (!mimeType) {
    return res.status(400).json({
      success: false,
      message: "MIME type is required.",
    });
  }

  const files = await FileSystem.findFilesByType(mimeType, {
    isPublic: true,
  })
    .skip(skip)
    .limit(parseInt(limit))
    .sort({ createdAt: -1 });

  const total = await FileSystem.countDocuments({
    type: "file",
    "fileDetails.mimeType": { $regex: mimeType, $options: "i" },
    isDeleted: false,
    isPublic: true,
  });

  const totalPages = Math.ceil(total / limit);

  res.status(200).json({
    success: true,
    data: files,
    pagination: {
      currentPage: parseInt(page),
      totalPages,
      totalItems: total,
      itemsPerPage: parseInt(limit),
    },
  });
});
