import FileSystem from "../models/folder.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";

// Create a new folder
export const createFolder = asyncHandler(async (req, res) => {
  const { name, parent, description } = req.body;
  const userId = req.user.id;

  const newFolder = new FileSystem({
    name,
    type: "folder",
    parent: parent || null,
    description,
    createdBy: userId,
    isPublic: true,
  });

  const savedFolder = await newFolder.save();
  res.status(201).json({
    success: true,
    message: "Folder created successfully.",
    data: savedFolder,
  });
});

// Upload/create a new file
export const createFile = asyncHandler(async (req, res) => {
  const {
    name,
    parent,
    title,
    description,
    tags,
    category,
    isPublic,
    isFeatured,
    fileDetails,
  } = req.body;
  const userId = req.user.id;

  const newFile = new FileSystem({
    name,
    type: "file",
    parent: parent || null,
    title,
    description,
    tags,
    category,
    isPublic: isPublic !== undefined ? isPublic : true,
    isFeatured: isFeatured || false,
    fileDetails,
    createdBy: userId,
    uploadedBy: userId,
  });

  const savedFile = await newFile.save();
  res.status(201).json({
    success: true,
    message: "File created successfully.",
    data: savedFile,
  });
});

// Get folder contents (files and subfolders)
export const getFolderContents = asyncHandler(async (req, res) => {
  const { folderId } = req.params;
  const { page = 1, limit = 20, type } = req.query;
  
  const skip = (page - 1) * limit;
  const query = {
    parent: folderId === "root" ? null : folderId,
    isDeleted: false,
  };
  
  if (type && ["folder", "file"].includes(type)) {
    query.type = type;
  }

  const items = await FileSystem.find(query)
    .skip(skip)
    .limit(parseInt(limit))
    .sort({ type: -1, name: 1 }); // Folders first, then files

  const total = await FileSystem.countDocuments(query);
  const totalPages = Math.ceil(total / limit);

  res.status(200).json({
    success: true,
    data: items,
    pagination: {
      currentPage: parseInt(page),
      totalPages,
      totalItems: total,
      itemsPerPage: parseInt(limit),
    },
  });
});

// Search files and folders
export const searchFileSystem = asyncHandler(async (req, res) => {
  const { query, type, tags, category, page = 1, limit = 20 } = req.query;
  const skip = (page - 1) * limit;

  const searchFilter = {
    isDeleted: false,
    $or: [
      { name: { $regex: query || "", $options: "i" } },
      { title: { $regex: query || "", $options: "i" } },
      { description: { $regex: query || "", $options: "i" } },
    ],
  };

  if (type && ["folder", "file"].includes(type)) {
    searchFilter.type = type;
  }

  if (tags) {
    searchFilter.tags = { $in: tags.split(",") };
  }

  if (category) {
    searchFilter.category = category;
  }

  const items = await FileSystem.find(searchFilter)
    .skip(skip)
    .limit(parseInt(limit))
    .sort({ type: -1, createdAt: -1 });

  const total = await FileSystem.countDocuments(searchFilter);
  const totalPages = Math.ceil(total / limit);

  res.status(200).json({
    success: true,
    data: items,
    pagination: {
      currentPage: parseInt(page),
      totalPages,
      totalItems: total,
      itemsPerPage: parseInt(limit),
    },
  });
});

// Get item by ID
export const getItemById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const item = await FileSystem.findById(id);
  if (!item || item.isDeleted) {
    return res.status(404).json({ 
      success: false, 
      message: "Item not found." 
    });
  }

  // Increment view count for files
  if (item.type === "file") {
    await item.incrementViews();
  }

  res.status(200).json({ success: true, data: item });
});

// Update item
export const updateItem = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  // Remove fields that shouldn't be updated directly
  delete updateData.type;
  delete updateData.createdBy;
  delete updateData.path;

  const updatedItem = await FileSystem.findByIdAndUpdate(
    id,
    updateData,
    { new: true, runValidators: true }
  );

  if (!updatedItem || updatedItem.isDeleted) {
    return res.status(404).json({ 
      success: false, 
      message: "Item not found." 
    });
  }

  res.status(200).json({
    success: true,
    message: "Item updated successfully.",
    data: updatedItem,
  });
});

// Soft delete items
export const deleteItems = asyncHandler(async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      success: false,
      message: "Please provide an array of IDs to delete.",
    });
  }

  let deletedCount = 0;
  for (const id of ids) {
    const item = await FileSystem.findById(id);
    if (item && !item.isDeleted) {
      await item.softDelete();
      deletedCount++;
    }
  }

  res.status(200).json({
    success: true,
    message: `${deletedCount} item(s) deleted successfully.`,
  });
});

// Permanently delete items
export const permanentDeleteItems = asyncHandler(async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      success: false,
      message: "Please provide an array of IDs to delete.",
    });
  }

  const deletedItems = await FileSystem.deleteMany({ _id: { $in: ids } });

  res.status(200).json({
    success: true,
    message: `${deletedItems.deletedCount} item(s) permanently deleted.`,
  });
});

// Restore deleted items
export const restoreItems = asyncHandler(async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      success: false,
      message: "Please provide an array of IDs to restore.",
    });
  }

  const restoredItems = await FileSystem.updateMany(
    { _id: { $in: ids }, isDeleted: true },
    { isDeleted: false }
  );

  res.status(200).json({
    success: true,
    message: `${restoredItems.modifiedCount} item(s) restored successfully.`,
  });
});

// Get files by type (images, documents, etc.)
export const getFilesByType = asyncHandler(async (req, res) => {
  const { mimeType, page = 1, limit = 20 } = req.query;
  const skip = (page - 1) * limit;

  if (!mimeType) {
    return res.status(400).json({
      success: false,
      message: "MIME type is required.",
    });
  }

  const files = await FileSystem.findFilesByType(mimeType, {
    isPublic: true,
  })
    .skip(skip)
    .limit(parseInt(limit))
    .sort({ createdAt: -1 });

  const total = await FileSystem.countDocuments({
    type: "file",
    "fileDetails.mimeType": { $regex: mimeType, $options: "i" },
    isDeleted: false,
    isPublic: true,
  });

  const totalPages = Math.ceil(total / limit);

  res.status(200).json({
    success: true,
    data: files,
    pagination: {
      currentPage: parseInt(page),
      totalPages,
      totalItems: total,
      itemsPerPage: parseInt(limit),
    },
  });
});
