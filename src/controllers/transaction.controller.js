import Transaction from "../models/transaction.model.js";
import Plan from "../models/plan.model.js";
import Coupon from "../models/coupon.model.js";
import User from "../models/user.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import { TRANSACTION_TYPES, TRANSACTION_STATUSES } from "../utils/constants.js";

/**
 * Create a new transaction (for subscription purchase/renewal)
 */
export const createTransaction = asyncHandler(async (req, res) => {
  const {
    user,
    subscription,
    planId,
    couponId,
    paymentMethod,
    paymentGateway,
    type = TRANSACTION_TYPES.SUBSCRIPTION_PURCHASE,
    billingDetails,
    metadata = {}
  } = req.body;

  // Fetch plan details
  const plan = await Plan.findById(planId);
  if (!plan) {
    return res.status(404).json({ 
      success: false, 
      message: "Plan not found" 
    });
  }

  // Fetch coupon details if provided
  let coupon = null;
  if (couponId) {
    coupon = await Coupon.findById(couponId);
    if (!coupon || !coupon.isActive) {
      return res.status(400).json({ 
        success: false, 
        message: "Invalid or inactive coupon" 
      });
    }
    
    // Check coupon validity
    const now = new Date();
    if (now < coupon.validFrom || now > coupon.validUntil) {
      return res.status(400).json({ 
        success: false, 
        message: "Coupon is not valid at this time" 
      });
    }
    
    // Check redemption limits
    if (coupon.maxRedemptions && coupon.currentRedemptions >= coupon.maxRedemptions) {
      return res.status(400).json({ 
        success: false, 
        message: "Coupon redemption limit exceeded" 
      });
    }
  }

  // Create transaction using the static method
  const transaction = Transaction.createSubscriptionTransaction({
    user,
    subscription,
    plan,
    coupon,
    paymentMethod,
    paymentGateway,
    type,
    billingDetails,
    metadata
  });

  const savedTransaction = await transaction.save();

  // Update coupon redemption count if coupon was used
  if (coupon) {
    coupon.currentRedemptions += 1;
    await coupon.save();
  }

  res.status(201).json({ 
    success: true, 
    transaction: savedTransaction 
  });
});

/**
 * Get all transactions with pagination
 */
export const getTransactions = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;
  
  const { status, type, user } = req.query;
  
  // Build filter object
  const filter = {};
  if (status) filter.status = status;
  if (type) filter.type = type;
  if (user) filter.user = user;

  const total = await Transaction.countDocuments(filter);
  const pages = Math.ceil(total / limit);
  
  const transactions = await Transaction.find(filter)
    .populate("user", "firstName lastName email")
    .populate("plan", "name price duration")
    .populate("subscription", "startDate endDate")
    .populate("coupon", "code discountType discountValue")
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);

  res.status(200).json({
    success: true,
    data: transactions,
    totalDocuments: total,
    currentPage: page,
    totalPages: pages,
  });
});

/**
 * Get transaction by ID
 */
export const getTransactionById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  const transaction = await Transaction.findById(id)
    .populate("user", "firstName lastName email")
    .populate("plan", "name price duration benefits")
    .populate("subscription", "startDate endDate status")
    .populate("coupon", "code discountType discountValue")
    .populate("parentTransaction", "type amount status")
    .populate("childTransactions", "type amount status");

  if (!transaction) {
    return res.status(404).json({ 
      success: false, 
      message: "Transaction not found" 
    });
  }

  res.status(200).json({ 
    success: true, 
    transaction 
  });
});

/**
 * Update transaction status
 */
export const updateTransactionStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { status, gatewayTransactionId, gatewayResponse } = req.body;

  const transaction = await Transaction.findById(id);
  if (!transaction) {
    return res.status(404).json({ 
      success: false, 
      message: "Transaction not found" 
    });
  }

  // Update transaction
  transaction.status = status;
  if (gatewayTransactionId) transaction.gatewayTransactionId = gatewayTransactionId;
  if (gatewayResponse) transaction.gatewayResponse = gatewayResponse;

  const updatedTransaction = await transaction.save();

  res.status(200).json({ 
    success: true, 
    transaction: updatedTransaction 
  });
});

/**
 * Process refund
 */
export const processRefund = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { refundAmount, refundReason, refundedBy } = req.body;

  const transaction = await Transaction.findById(id);
  if (!transaction) {
    return res.status(404).json({ 
      success: false, 
      message: "Transaction not found" 
    });
  }

  if (transaction.status !== TRANSACTION_STATUSES.COMPLETED) {
    return res.status(400).json({ 
      success: false, 
      message: "Can only refund completed transactions" 
    });
  }

  if (transaction.refund.isRefunded) {
    return res.status(400).json({ 
      success: false, 
      message: "Transaction already refunded" 
    });
  }

  // Update refund details
  transaction.refund = {
    isRefunded: true,
    refundAmount: refundAmount || transaction.amount,
    refundReason,
    refundDate: new Date(),
    refundedBy
  };
  transaction.status = TRANSACTION_STATUSES.REFUNDED;

  const updatedTransaction = await transaction.save();

  res.status(200).json({ 
    success: true, 
    transaction: updatedTransaction 
  });
});

/**
 * Get user's transaction history
 */
export const getUserTransactions = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  const total = await Transaction.countDocuments({ user: userId });
  const pages = Math.ceil(total / limit);
  
  const transactions = await Transaction.find({ user: userId })
    .populate("plan", "name price duration")
    .populate("subscription", "startDate endDate")
    .populate("coupon", "code discountType discountValue")
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);

  res.status(200).json({
    success: true,
    data: transactions,
    totalDocuments: total,
    currentPage: page,
    totalPages: pages,
  });
});

/**
 * Get transaction analytics/stats
 */
export const getTransactionStats = asyncHandler(async (req, res) => {
  const { startDate, endDate } = req.query;
  
  const matchStage = {};
  if (startDate || endDate) {
    matchStage.createdAt = {};
    if (startDate) matchStage.createdAt.$gte = new Date(startDate);
    if (endDate) matchStage.createdAt.$lte = new Date(endDate);
  }

  const stats = await Transaction.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalTransactions: { $sum: 1 },
        totalRevenue: { $sum: "$amount" },
        totalDiscounts: { $sum: "$discountAmount" },
        avgTransactionValue: { $avg: "$amount" },
        completedTransactions: {
          $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] }
        },
        failedTransactions: {
          $sum: { $cond: [{ $eq: ["$status", "failed"] }, 1, 0] }
        },
        freeTransactions: {
          $sum: { $cond: [{ $eq: ["$amount", 0] }, 1, 0] }
        }
      }
    }
  ]);

  const result = stats[0] || {
    totalTransactions: 0,
    totalRevenue: 0,
    totalDiscounts: 0,
    avgTransactionValue: 0,
    completedTransactions: 0,
    failedTransactions: 0,
    freeTransactions: 0
  };

  res.status(200).json({ 
    success: true, 
    stats: result 
  });
});
