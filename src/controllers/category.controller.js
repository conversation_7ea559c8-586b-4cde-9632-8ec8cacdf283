import Category from "../models/category.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import { slugGenerator } from "../utils/slug-generator.js";
/**
 * Create a new category
 */
export const createCategory = asyncHandler(async (req, res) => {
  const { name, slug,type, description, parent } = req.body;

  // Generate slug
  const genratedSlug = slug ? slugGenerator(slug) : slugGenerator(name);

  // Check if category already exists with the same name ignoring case

  const existingCategory = await Category.findOne({
    name: { $regex: new RegExp(name, "i") },
  });
  // console.log("existingCategory", existingCategory);
  if (existingCategory) {
    return res
      .status(400)
      .json({ success: false, message: "Category already exists" });
  }

  const newCategory = new Category({
    name,
    slug: genratedSlug,
    description,
    parent,
  });

  const savedCategory = await newCategory.save();
  res.status(201).json({ success: true, data: savedCategory });
});

/**
 * Get all categories
 */
export const getCategories = asyncHandler(async (req, res) => {
  const categories = await Category.find();

  res.status(200).json({ success: true, data: categories });
});

// get active categories
export const getActiveCategories = asyncHandler(async (req, res) => {
  const categories = await Category.find({ isActive: true });
  res.status(200).json({ success: true, data: categories });
});

// create getcategorieswithpagination

export const getCategoriesWithPagination = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1; // default page 1
  const limit = parseInt(req.query.limit) || 10; // default limit 10
  const skip = (page - 1) * limit; // calculate the offset value
  const total = await Category.countDocuments(); // get total categories count
  const pages = Math.ceil(total / limit); // calculate total pages
  const categories = await Category.find() // get categories
    // .populate("parent", "name")
    .skip(skip)
    .limit(limit);
  //instead hasMore send next page number
  res.status(200).json({
    success: true,
    data: categories,
    totalDocuments: total,
    currentPage: page,
    totalPages: pages,
  });
});

// cursor based pagination
export const getCategoriesWithPaginationCursor = asyncHandler(
  async (req, res) => {
    const limit = Math.min(50, Math.max(1, parseInt(req.query.limit) || 10)); // Limit results
    const lastId = req.query.lastId; // Cursor (last fetched document's ID)

    const query = lastId ? { _id: { $gt: lastId } } : {}; // Query to fetch documents after the last ID
    const categories = await Category.find(query)
      // .populate("parent", "name")
      .limit(limit)
      .sort({ _id: 1 }); // Sort by ID to maintain order

    const hasNextPage = categories.length === limit;

    res.status(200).json({
      success: true,
      data: categories,
      hasNextPage,
      nextCursor: hasNextPage ? categories[categories.length - 1]._id : null, // Pass the last ID as the cursor
    });
  }
);

// get categories with aggregation
export const getCategoriesWithPaginationAggregation = asyncHandler(
  async (req, res) => {
    const page = Math.max(1, parseInt(req.query.page) || 1);
    const limit = Math.min(50, Math.max(1, parseInt(req.query.limit) || 10));
    const skip = (page - 1) * limit;

    const results = await Category.aggregate([
      { $match: {} }, // Add any filters here
      {
        $lookup: {
          from: "categories",
          localField: "parent",
          foreignField: "_id",
          as: "parent",
        },
      },
      {
        $unwind: { path: "$parent", preserveNullAndEmptyArrays: true },
      },
      { $project: { name: 1, "parent.name": 1 } }, // Fetch only necessary fields
      { $skip: skip },
      { $limit: limit },
    ]);

    const totalDocuments = await Category.countDocuments();
    const totalPages = Math.ceil(totalDocuments / limit);

    res.status(200).json({
      success: true,
      data: results,
      totalDocuments,
      currentPage: page,
      totalPages,
    });
  }
);

/**
 * Get a single category by ID
 */
export const getCategoryById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const category = await Category.findById(id);
  // .populate(
  //   "parent",
  //   "name"
  // );
  if (!category) {
    return res
      .status(404)
      .json({ success: false, message: "Category not found" });
  }
  res.status(200).json({
    success: true,
    data: category,
    message: "Category fetched successfully",
  });
});

/**
 * Update a category
 */
export const updateCategory = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updates = req.body;

  const updatedCategory = await Category.findByIdAndUpdate(id, updates, {
    new: true,
  });
  // .populate("parent", "name");

  if (!updatedCategory) {
    return res
      .status(404)
      .json({ success: false, message: "Category not found" });
  }
  res.status(200).json({
    success: true,
    data: updatedCategory,
    message: "Category updated successfully",
  });
});

/**
 * Delete a category
 */
export const deleteCategory = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const deletedCategory = await Category.findByIdAndDelete(id);
  if (!deletedCategory) {
    return res
      .status(404)
      .json({ success: false, message: "Category not found" });
  }

  res
    .status(200)
    .json({ success: true, message: "Category deleted successfully" });
});
