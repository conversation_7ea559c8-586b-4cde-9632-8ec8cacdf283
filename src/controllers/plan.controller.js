import Plan from "../models/plan.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";

/**
 * Create a new plan
 */
export const createPlan = asyncHandler(async (req, res) => {
  const {
    name,
    description,
    price,
    currency,
    duration,
    isActive,
    maxTemplates,
    maxUsers,
    benefits,
    createdBy,
  } = req.body;

  const newPlan = new Plan({
    name,
    description,
    price,
    currency,
    duration,
    isActive,
    maxTemplates,
    maxUsers,
    benefits,
    createdBy,
  });

  const savedPlan = await newPlan.save();
  res.status(201).json({ success: true, data: savedPlan });
});

// get plans with pagination
export const getPlansWithPagination = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;
  const total = await Plan.countDocuments();
  const pages = Math.ceil(total / limit);
  const plans = await Plan.find()
    .populate("createdBy", "name email")
    .skip(skip)
    .limit(limit);

  res.status(200).json({
    success: true,
    data: plans,
    totalDocuments: total,
    currentPage: page,
    totalPages: pages,
  });
});

/**
 * Get all plans
 */
export const getPlans = asyncHandler(async (req, res) => {
  const plans = await Plan.find().populate("createdBy", "name email");
  res.status(200).json({ success: true, data: plans });
});

/**
 * Get a single plan by ID
 */
export const getPlanById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const plan = await Plan.findById(id);
  // .populate("createdBy", "name email");

  if (!plan) {
    res.status(404);
    throw new Error("Plan not found");
  }

  res.status(200).json({ success: true, data: plan });
});

/**
 * Update a plan
 */
export const updatePlan = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updates = req.body;

  const updatedPlan = await Plan.findByIdAndUpdate(id, updates, {
    new: true,
  });

  if (!updatedPlan) {
    res.status(404);
    throw new Error("Plan not found");
  }

  res.status(200).json({ success: true, data: updatedPlan });
});

/**
 * Delete a plan
 */
export const deletePlan = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const deletedPlan = await Plan.findByIdAndDelete(id);

  if (!deletedPlan) {
    res.status(404);
    throw new Error("Plan not found");
  }

  res.status(200).json({ success: true, message: "Plan deleted successfully" });
});
