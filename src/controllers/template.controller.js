import Template from "../models/template.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import { slugGenerator } from "../utils/slug-generator.js";

/**
 * Create a new template
 */
export const createTemplate = asyncHandler(async (req, res) => {
  const {
    name,
    slug,
    description,
    categories,
    superCategory,
    tags,
    price,
    isPaid,
    previewUrl,
    authorId,
    keyFeatures,
    highlights,
    status,
    isFeatured,
    discount,
  } = req.body;

  const genratedSlug = slug ? slugGenerator(slug) : slugGenerator(name);

  const newTemplate = new Template({
    name,
    description,
    categories,
    superCategory,
    tags,
    price,
    isPaid,
    previewUrl,
    authorId,
    keyFeatures,
    highlights,
    status,
    isFeatured,
    discount,
  });

  const savedTemplate = await newTemplate.save();
  res.status(201).json({ success: true, template: savedTemplate });
});

/**
 * Get all templates
 */
export const getTemplates = asyncHandler(async (req, res) => {
  const templates = await Template.find()
    .populate("categories")
    .populate("superCategory")
    .populate("authorId", "name email");

  res.status(200).json({ success: true, templates });
});

/**
 * Get a single template by ID
 */
export const getTemplateById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const template = await Template.findById(id)
    .populate("categories")
    .populate("superCategory")
    .populate("authorId", "name email");

  if (!template) {
    res.status(404);
    throw new Error("Template not found");
  }

  res.status(200).json({ success: true, template });
});

/**
 * Update a template
 */
export const updateTemplate = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updates = req.body;

  const updatedTemplate = await Template.findByIdAndUpdate(id, updates, {
    new: true,
  });

  if (!updatedTemplate) {
    res.status(404);
    throw new Error("Template not found");
  }

  res.status(200).json({ success: true, template: updatedTemplate });
});

/**
 * Delete a template
 */
export const deleteTemplate = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const deletedTemplate = await Template.findByIdAndDelete(id);

  if (!deletedTemplate) {
    res.status(404);
    throw new Error("Template not found");
  }

  res
    .status(200)
    .json({ success: true, message: "Template deleted successfully" });
});
