import User from "../models/user.model.js";
import Plan from "../models/plan.model.js";
import bcrypt from "bcryptjs";
import { generateToken } from "../utils/jwt.js"; // Utility for generating JWT token
import { async<PERSON>and<PERSON> } from "../utils/asyncHandler.js";

// Create User (Admin only)
export const createUser = async (req, res) => {
  try {
    const {
      email,
      password,
      firstName,
      lastName,
      username,
      profileImage,
      role,
      googleId,
    } = req.body;

    // Check if user exists
    const userExists = await User.findOne({
      $or: [{ email }, { username }]
    });
    if (userExists) {
      return res.status(400).json({
        success: false,
        message: userExists.email === email
          ? "User with this email already exists"
          : "Username already taken"
      });
    }

    // Hash password if provided
    let hashedPassword = null;
    if (password) {
      hashedPassword = await bcrypt.hash(password, 12);
    }

    // Create new user
    const newUser = new User({
      email,
      password: hashedPassword,
      firstName,
      lastName,
      username,
      profileImage,
      role,
      googleId,
      provider: googleId ? "google" : "email",
      isEmailVerified: googleId ? true : false, // Google users are pre-verified
    });

    const savedUser = await newUser.save();

    res.status(201).json({
      success: true,
      message: "User created successfully",
      data: { user: savedUser }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: "Error creating user"
    });
  }
};

// Get All Users
export const getUsers = async (req, res) => {
  try {
    const users = await User.find({ isDeleted: false })
      .populate('currentPlan')
      .select('-password -emailVerificationToken -passwordResetToken');

    res.status(200).json({
      success: true,
      data: { users }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: "Error fetching users"
    });
  }
};

// Get User with pagination
export const getUsersWithPagination = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;
  const total = await User.countDocuments();
  const pages = Math.ceil(total / limit);
  const users = await User.find({ isDeleted: false })
    // .populate("activeSubscription")
    .skip(skip)
    .limit(limit);

  res.status(200).json({
    data: users,
    totalDocuments: total,
    currentPage: page,
    totalPages: pages,
  });
});

// Get Single User
export const getUserById = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    // .populate(
    //   "activeSubscription"
    // );
    console.log(user);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    res.status(200).json({ user });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error fetching user" });
  }
};

// Update User
export const updateUser = async (req, res) => {
  try {
    const { email, password, ...updateData } = req.body;

    if (password) {
      updateData.password = await bcrypt.hash(password, 12); // Re-hash new password if provided
    }

    const updatedUser = await User.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true }
    );
    if (!updatedUser) {
      return res.status(404).json({ message: "User not found" });
    }
    res.status(200).json({ user: updatedUser });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error updating user" });
  }
};

// Delete User (Soft Delete)
export const deleteUser = async (req, res) => {
  try {
    const deletedUser = await User.findByIdAndUpdate(
      req.params.id,
      { isDeleted: true },
      { new: true }
    );
    if (!deletedUser) {
      return res.status(404).json({ message: "User not found" });
    }
    res.status(200).json({ message: "User deleted successfully" });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error deleting user" });
  }
};

// This function is now handled in auth.controller.js
// Keeping this comment for reference - login functionality moved to auth controller

// Assign Subscription to User
export const assignSubscription = async (req, res) => {
  try {
    const { userId, planId } = req.body;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found"
      });
    }

    const plan = await Plan.findById(planId);
    if (!plan) {
      return res.status(404).json({
        success: false,
        message: "Plan not found"
      });
    }

    // Activate subscription using the user model method
    await user.activateSubscription(plan);

    res.status(200).json({
      success: true,
      message: "Subscription assigned successfully",
      data: {
        user: {
          id: user._id,
          isPro: user.isPro,
          isLifetimePro: user.isLifetimePro,
          subscriptionSummary: user.getSubscriptionSummary()
        }
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: "Error assigning subscription"
    });
  }
};
