import { asyncHandler } from "../utils/asyncHandler.js";
import User from "../models/user.model.js";
import Product from "../models/product.model.js";
import Access from "../models/access.model.js";
import Download from "../models/download.model.js";
import ResourceDeliveryService from "../services/resourceDelivery.service.js";

/**
 * Check user access to a specific product
 */
export const checkProductAccess = asyncHandler(async (req, res) => {
  const { productId } = req.params;
  const { userId } = req.auth;

  const user = await User.findOne({ clerkId: userId });
  const product = await Product.findById(productId);

  if (!user || !product) {
    return res.status(404).json({
      success: false,
      message: "User or product not found",
    });
  }

  const hasAccess = user.hasAccessToProduct(product);
  const contentInfo = product.getContentForUser(user);

  res.json({
    success: true,
    data: {
      hasAccess,
      product: {
        id: product._id,
        name: product.name,
        slug: product.slug,
        accessLevel: product.accessLevel,
        requiredSubscription: product.requiredSubscription,
        isPaid: product.isPaid,
      },
      user: {
        subscriptionTier: user.subscriptionTier,
        subscriptionStatus: user.subscriptionStatus,
        accessLevel: user.accessLevel,
        downloadCount: user.downloadCount,
        monthlyDownloadLimit: user.monthlyDownloadLimit,
      },
      content: contentInfo,
    },
  });
});

/**
 * Generate secure download link for authorized users
 */
export const generateDownloadLink = asyncHandler(async (req, res) => {
  const { productId } = req.params;
  const { userId } = req.auth;

  const user = await User.findOne({ clerkId: userId });
  const product = await Product.findById(productId);

  if (!user || !product) {
    return res.status(404).json({
      success: false,
      message: "User or product not found",
    });
  }

  // Check if user can download
  if (!user.canDownload()) {
    return res.status(429).json({
      success: false,
      message: "Download limit exceeded",
      currentDownloads: user.downloadCount,
      monthlyLimit: user.monthlyDownloadLimit,
    });
  }

  try {
    let downloadInfo;

    // Handle free content (GitHub redirect)
    if (!product.isPaid || product.accessLevel === "free") {
      downloadInfo = ResourceDeliveryService.generateGitHubRedirectUrl(product, user);
      
      // Track the download
      await Download.createDownloadSession(
        user._id,
        product._id,
        "github_redirect",
        {
          ipAddress: req.ip,
          userAgent: req.get("User-Agent"),
          referrer: req.get("Referrer"),
          subscriptionTier: user.subscriptionTier,
          accessLevel: user.accessLevel,
        }
      );
    } else {
      // Handle paid content (secure download)
      downloadInfo = await ResourceDeliveryService.generateSecureDownloadToken(
        user._id,
        product._id,
        {
          ipAddress: req.ip,
          userAgent: req.get("User-Agent"),
          referrer: req.get("Referrer"),
          downloadSource: "web",
        }
      );
    }

    res.json({
      success: true,
      data: downloadInfo,
    });
  } catch (error) {
    console.error("Error generating download link:", error);
    res.status(403).json({
      success: false,
      message: error.message || "Access denied",
    });
  }
});

/**
 * Handle secure file download with token validation
 */
export const secureDownload = asyncHandler(async (req, res) => {
  const { token } = req.params;

  try {
    const { downloadSession, user, product } = await ResourceDeliveryService.validateDownloadToken(token);

    // Generate presigned URL for actual file download
    const presignedUrl = await ResourceDeliveryService.createPresignedUrl(
      product,
      downloadSession,
      30 // 30 minutes expiry for presigned URL
    );

    // Track download initiation
    await ResourceDeliveryService.trackDownloadCompletion(downloadSession._id, {
      ipAddress: req.ip,
    });

    // Redirect to presigned URL or return download info
    if (req.query.redirect === "true") {
      res.redirect(presignedUrl.url);
    } else {
      res.json({
        success: true,
        data: {
          downloadUrl: presignedUrl.url,
          fileName: presignedUrl.fileName,
          fileSize: presignedUrl.fileSize,
          fileType: presignedUrl.fileType,
          expiresAt: presignedUrl.expiresAt,
        },
      });
    }
  } catch (error) {
    console.error("Secure download error:", error);
    res.status(403).json({
      success: false,
      message: error.message || "Invalid or expired download token",
    });
  }
});

/**
 * Get user's download history
 */
export const getDownloadHistory = asyncHandler(async (req, res) => {
  const { userId } = req.auth;
  const page = parseInt(req.query.page) || 1;
  const limit = Math.min(parseInt(req.query.limit) || 20, 100);

  const user = await User.findOne({ clerkId: userId });
  if (!user) {
    return res.status(404).json({
      success: false,
      message: "User not found",
    });
  }

  const downloadHistory = await ResourceDeliveryService.getUserDownloadHistory(
    user._id,
    { page, limit }
  );

  res.json({
    success: true,
    data: downloadHistory,
  });
});

/**
 * Get user's access permissions for all products
 */
export const getUserAccessPermissions = asyncHandler(async (req, res) => {
  const { userId } = req.auth;

  const user = await User.findOne({ clerkId: userId });
  if (!user) {
    return res.status(404).json({
      success: false,
      message: "User not found",
    });
  }

  // Get all user's access records
  const accessRecords = await Access.find({
    userId: user._id,
    isActive: true,
  }).populate("productId", "name slug type accessLevel requiredSubscription isPaid");

  // Get download stats
  const downloadStats = await Download.getUserDownloadStats(user._id);

  res.json({
    success: true,
    data: {
      user: {
        subscriptionTier: user.subscriptionTier,
        subscriptionStatus: user.subscriptionStatus,
        accessLevel: user.accessLevel,
        downloadCount: user.downloadCount,
        monthlyDownloadLimit: user.monthlyDownloadLimit,
      },
      accessRecords,
      downloadStats: downloadStats[0] || {
        totalDownloads: 0,
        totalSize: 0,
        uniqueProductsCount: 0,
      },
    },
  });
});

/**
 * Grant access to a product (Admin only)
 */
export const grantProductAccess = asyncHandler(async (req, res) => {
  const { userId, productId, accessType, expiresAt } = req.body;

  const user = await User.findById(userId);
  const product = await Product.findById(productId);

  if (!user || !product) {
    return res.status(404).json({
      success: false,
      message: "User or product not found",
    });
  }

  const access = await Access.grantAccess(userId, productId, accessType, {
    expiresAt: expiresAt ? new Date(expiresAt) : undefined,
  });

  res.json({
    success: true,
    data: access,
    message: "Access granted successfully",
  });
});

/**
 * Revoke access to a product (Admin only)
 */
export const revokeProductAccess = asyncHandler(async (req, res) => {
  const { userId, productId } = req.body;

  const access = await Access.revokeAccess(userId, productId);

  if (!access) {
    return res.status(404).json({
      success: false,
      message: "Access record not found",
    });
  }

  res.json({
    success: true,
    data: access,
    message: "Access revoked successfully",
  });
});

export default {
  checkProductAccess,
  generateDownloadLink,
  secureDownload,
  getDownloadHistory,
  getUserAccessPermissions,
  grantProductAccess,
  revokeProductAccess,
};
