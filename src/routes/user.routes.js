import express from "express";
import {
  createUser,
  getUsers,
  getUserById,
  updateUser,
  deleteUser,
  assignSubscription,
  getUsersWithPagination,
} from "../controllers/user.controller.js";
import { authorize } from "../middlewares/auth.middleware.js";
import {
  createUserValidator,
  updateUserValidator,
  userIdValidator,
  assignSubscriptionValidator,
} from "../validators/user.validator.js";
import { validateRequest } from "../middlewares/validateRequest.middleware.js";

const router = express.Router();

// Admin-only routes
router.use(authorize(["admin", "super_admin"])); // Middleware to restrict access to admin only
router.get("/", getUsers); // Get all users
router.get("/pagination", getUsersWithPagination); //fetch users with pagination
router.get("/:id", getUserById); // Get a single user
router.put("/:id", updateUserValidator, validateRequest, updateUser); // Update user (self or admin)
router.delete("/:id", userIdValidator, validateRequest, deleteUser); // Delete user (admin only)
// Subscription Management
router.post(
  "/:id/subscribe",
  assignSubscriptionValidator,
  validateRequest,
  assignSubscription
); // Assign subscription to user (admin only)

export default router;
