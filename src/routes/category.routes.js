import express from "express";
import {
  createCategory,
  getCategories,
  getCategoryById,
  updateCategory,
  deleteCategory,
  getCategoriesWithPagination,
  getActiveCategories,
} from "../controllers/category.controller.js";
import { validateCategory } from "../validators/category.validator.js";
import { protect, authorize } from "../middlewares/auth.middleware.js";

const router = express.Router();

/**
 * @route GET /categories/active
 * @desc Get active categories
 */
router.get("/active", getActiveCategories);

router.use(protect);
router.use(authorize(["admin", "super_admin"]));
/**
 * @route GET /categories
 * @desc Get all categories
 */
router.get("/", getCategories);

/**
 * @route POST /categories
 * @desc Create a new category
 */
router.post("/", validateCategory, createCategory);

/**
 * @route GET /categories (pagination)
 * @desc Get all categories with pagination
 */

router.get("/pagination", getCategoriesWithPagination);

/**
 * @route GET /categories/:id
 * @desc Get a category by ID
 */
router.get("/:id", getCategoryById);

/**
 * @route PUT /categories/:id
 * @desc Update a category
 */
router.put("/:id", validateCategory, updateCategory);

/**
 * @route DELETE /categories/:id
 * @desc Delete a category
 */
router.delete("/:id", deleteCategory);

export default router;
