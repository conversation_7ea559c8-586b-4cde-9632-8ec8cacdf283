import express from "express";
import {
  createPlan,
  getPlans,
  getPlanById,
  updatePlan,
  deletePlan,
  getPlansWithPagination,
} from "../controllers/plan.controller.js";
import { validatePlan } from "../validators/plan.validator.js";
import { protect, authorize } from "../middlewares/auth.middleware.js";

const router = express.Router();

/**
 * @route GET /api/plans
 * @desc Get all plans
 * @access Public
 */
router.get("/", getPlans);

router.use(protect);
router.use(authorize(["admin", "super_admin"]));

/**
 * @route POST /api/plans
 * @desc Create a new plan
 * @access Private (Admin only)
 */
router.post("/", validatePlan, createPlan);

/**
 * @route GET /api/plans/pagination
 * @desc Get all plans with pagination
 * @access Public
 */
router.get("/pagination", getPlansWithPagination);

/**
 * @route GET /api/plans/:id
 * @desc Get a single plan by ID
 * @access Public
 */
router.get("/:id", getPlanById);

/**
 * @route PUT /api/plans/:id
 * @desc Update a plan by ID
 * @access Private (Admin only)
 */
router.put("/:id", validatePlan, updatePlan);

/**
 * @route DELETE /api/plans/:id
 * @desc Delete a plan by ID
 * @access Private (Admin only)
 */
router.delete("/:id", deletePlan);

export default router;
