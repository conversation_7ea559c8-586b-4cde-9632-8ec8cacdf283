import express from "express";
import { protect, authorize } from "../middlewares/auth.middleware.js";
import { validateFolder, validateFolderUpdate } from "../validators/folder.validator.js";
import {
  createFolder,
  getFolderContents,
  searchFileSystem,
  getItemById,
  updateItem,
  deleteItems,
  permanentDeleteItems,
  restoreItems,
} from "../controllers/fileSystem.controller.js";

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Public routes (authenticated users)
router.get("/:folderId/contents", getFolderContents); // Get folder contents
router.get("/search", searchFileSystem); // Search folders
router.get("/:id", getItemById); // Get folder by ID

// Admin/Super Admin routes
router.use(authorize(["admin", "super_admin"]));

// Folder management
router.post("/", validateFolder, createFolder); // Create folder
router.put("/:id", validateFolderUpdate, updateItem); // Update folder
router.delete("/soft-delete", deleteItems); // Soft delete folders
router.delete("/permanent-delete", permanentDeleteItems); // Permanently delete folders
router.post("/restore", restoreItems); // Restore deleted folders

export default router;
