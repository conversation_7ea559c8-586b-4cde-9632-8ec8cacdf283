// authRoutes.js
import express from "express";
import passport from "passport";
import {
  registerUser,
  loginUser,
  verifyEmail,
  forgotPassword,
  resetPassword,
  getCurrentUser,
  logoutUser,
  googleAuthSuccess,
  googleAuthFailure,
} from "../controllers/auth.controller.js";
import { protect } from "../middlewares/auth.middleware.js";
import {
  registerValidator,
  loginValidator,
  forgotPasswordValidator,
  resetPasswordValidator,
  verifyEmailValidator
} from "../validators/auth.validator.js";
import { validateRequest } from "../middlewares/validateRequest.middleware.js";

const router = express.Router();

// Email Authentication Routes
router.post("/register", registerValidator, validateRequest, registerUser);
router.post("/login", loginValidator, validateRequest, loginUser);
router.post("/verify-email", verifyEmailValidator, validateRequest, verifyEmail);
router.post("/forgot-password", forgotPasswordValidator, validateRequest, forgotPassword);
router.post("/reset-password", resetPasswordValidator, validateRequest, resetPassword);

// Google OAuth Routes (only if Google OAuth is configured)
if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
  router.get("/google", passport.authenticate("google", { scope: ["profile", "email"] }));
  router.get("/google/callback",
    passport.authenticate("google", { session: false, failureRedirect: "/api/auth/google/failure" }),
    googleAuthSuccess
  );
  router.get("/google/success", googleAuthSuccess);
  router.get("/google/failure", googleAuthFailure);
} else {
  // Provide fallback routes that return appropriate messages
  router.get("/google", (req, res) => {
    res.status(503).json({
      success: false,
      message: "Google OAuth is not configured on this server"
    });
  });
  router.get("/google/callback", (req, res) => {
    res.status(503).json({
      success: false,
      message: "Google OAuth is not configured on this server"
    });
  });
}

// Protected Routes
router.get("/me", protect, getCurrentUser);
router.post("/logout", protect, logoutUser);

export default router;
