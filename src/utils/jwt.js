import jwt from 'jsonwebtoken';

/**
 * Generate a JWT token for the user
 * @param {String} id - User ID
 * @returns {String} JWT token
 */
export const generateToken = (id) => {
  const payload = { id };
  const secret = process.env.JWT_SECRET;

  if (!secret) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }

  return jwt.sign(payload, secret, {
    expiresIn: process.env.JWT_EXPIRES_IN || '30d', // Default to 30 days if not specified
  });
};

/**
 * Generate email verification token
 * @returns {String} JWT token
 */
export const generateEmailVerificationToken = () => {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }

  return jwt.sign({ purpose: "email_verification" }, secret, {
    expiresIn: "24h",
  });
};

/**
 * Generate password reset token
 * @returns {String} JWT token
 */
export const generatePasswordResetToken = () => {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }

  return jwt.sign({ purpose: "password_reset" }, secret, {
    expiresIn: "1h",
  });
};

/**
 * Verify a JWT token
 * @param {String} token - JWT token
 * @returns {Object} Decoded payload
 */
export const verifyToken = (token) => {
  const secret = process.env.JWT_SECRET;

  if (!secret) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }

  try {
    return jwt.verify(token, secret);
  } catch (err) {
    throw new Error('Token is invalid or expired');
  }
};
