import { body, param } from "express-validator";

// Validator for creating a new template


export const createTemplateValidator = [
  body("name")
    .isString()
    .notEmpty()
    .withMessage("Template name is required.")
    .isLength({ min: 3 })
    .withMessage("Template name must be at least 3 characters long."),
  body("description")
    .optional()
    .isString()
    .isLength({ max: 500 })
    .withMessage("Description can be up to 500 characters."),
  body("categories")
    .optional()
    .isArray()
    .withMessage("Categories must be an array of category IDs."),
  body("superCategory")
    .optional()
    .isMongoId()
    .withMessage("Invalid super category ID."),
  body("tags")
    .optional()
    .isArray()
    .withMessage("Tags must be an array of strings."),
  body("price")
    .optional()
    .isFloat({ min: 0 })
    .withMessage("Price must be a positive number."),
  body("isPaid")
    .optional()
    .isBoolean()
    .withMessage("isPaid must be a boolean value."),
  body("previewUrl")
    .optional()
    .isURL()
    .withMessage("Preview URL must be a valid URL."),
  body("authorId").isMongoId().withMessage("Invalid author ID."),
  body("keyFeatures")
    .optional()
    .isArray()
    .withMessage("Key features must be an array of strings."),
  body("highlights")
    .optional()
    .isArray()
    .withMessage("Highlights must be an array of strings."),
  body("status")
    .optional()
    .isIn(["active", "inactive"])
    .withMessage("Status must be either 'active' or 'inactive'."),
  body("isFeatured")
    .optional()
    .isBoolean()
    .withMessage("isFeatured must be a boolean value."),
  body("discount")
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage("Discount must be a number between 0 and 100."),
];

// Validator for updating an existing template
export const updateTemplateValidator = [
  param("id").isMongoId().withMessage("Invalid template ID."),
  body("name")
    .optional()
    .isString()
    .isLength({ min: 3 })
    .withMessage("Template name must be at least 3 characters long."),
  body("description")
    .optional()
    .isString()
    .isLength({ max: 500 })
    .withMessage("Description can be up to 500 characters."),
  body("price")
    .optional()
    .isFloat({ min: 0 })
    .withMessage("Price must be a positive number."),
  body("categories")
    .optional()
    .isArray()
    .withMessage("Categories must be an array of category IDs."),
  body("superCategory")
    .optional()
    .isMongoId()
    .withMessage("Invalid super category ID."),
  body("tags")
    .optional()
    .isArray()
    .withMessage("Tags must be an array of strings."),
  body("isPaid")
    .optional()
    .isBoolean()
    .withMessage("isPaid must be a boolean value."),
  body("previewUrl")
    .optional()
    .isURL()
    .withMessage("Preview URL must be a valid URL."),
  body("keyFeatures")
    .optional()
    .isArray()
    .withMessage("Key features must be an array of strings."),
  body("highlights")
    .optional()
    .isArray()
    .withMessage("Highlights must be an array of strings."),
  body("status")
    .optional()
    .isIn(["active", "inactive"])
    .withMessage("Status must be either 'active' or 'inactive'."),
  body("isFeatured")
    .optional()
    .isBoolean()
    .withMessage("isFeatured must be a boolean value."),
  body("discount")
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage("Discount must be a number between 0 and 100."),
];

// Validator for checking template ID
export const templateIdValidator = [
  param("id").isMongoId().withMessage("Invalid template ID."),
];
