import { body, validationResult } from "express-validator";

export const validatePlan = [
  // Name validation
  body("name")
    .notEmpty()
    .withMessage("Plan name is required.")
    .isString()
    .withMessage("Plan name must be a string."),

  // Price validation
  body("price")
    .notEmpty()
    .withMessage("Price is required.")
    .isNumeric()
    .withMessage("Price must be a number.")
    .custom((value) => value >= 0)
    .withMessage("Price must be greater than or equal to 0."),

  // Currency validation
  body("currency")
    .optional()
    .isString()
    .withMessage("Currency must be a string.")
    .isLength({ max: 3 })
    .withMessage("Currency must be a valid ISO currency code (e.g., USD)."),

  // Duration validation
  body("duration")
    .optional()
    .isIn(["monthly", "yearly", "lifetime"])
    .withMessage(
      "Duration must be one of the following values: 'monthly', 'yearly', 'lifetime'."
    ),

  // MaxTemplates validation
  body("maxTemplates")
    .optional()
    .isInt({ min: 0 })
    .withMessage("Max templates must be a positive integer."),

  // MaxUsers validation
  body("maxUsers")
    .optional()
    .isInt({ min: 0 })
    .withMessage("Max users must be a positive integer."),

  // Benefits validation
  // body("benefits")
  //   .optional()
  //   .isArray()
  //   .withMessage("Benefits must be an array of strings.")
  //   .custom((benefits) =>
  //     benefits.every((benefit) => typeof benefit === "string")
  //   )
  //   .withMessage("Each benefit must be a string."),

  // Benefits validation - now accepts JSON objects
  body("benefits")
    .optional()
    .custom((value) => {
      if (typeof value === 'string') {
        try {
          JSON.parse(value);
          return true;
        } catch (e) {
          return false;
        }
      }
      return typeof value === 'object';
    })
    .withMessage("Benefits must be a valid JSON object or string."),

  // Validate and handle errors
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array(),
      });
    }
    next();
  },
];
