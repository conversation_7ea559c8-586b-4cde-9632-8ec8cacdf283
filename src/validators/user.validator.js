import { body, param } from "express-validator";

// Create User Validator
export const createUserValidator = [
  body("email").isEmail().withMessage("Email is required and must be valid"),
  body("password")
    .isLength({ min: 6 })
    .withMessage("Password must be at least 6 characters"),
  body("firstName").notEmpty().withMessage("First name is required"),
  body("lastName").notEmpty().withMessage("Last name is required"),
];

// Update User Validator
export const updateUserValidator = [
  body("email").optional().isEmail().withMessage("Email must be valid"),
  body("password")
    .optional()
    .isLength({ min: 6 })
    .withMessage("Password must be at least 6 characters"),
  body("firstName")
    .optional()
    .notEmpty()
    .withMessage("First name cannot be empty"),
  body("lastName")
    .optional()
    .notEmpty()
    .withMessage("Last name cannot be empty"),
];

// User ID Validator
export const userIdValidator = [
  param("id").isMongoId().withMessage("Invalid user ID"),
];

// Assign Subscription Validator
export const assignSubscriptionValidator = [
  body("userId").isMongoId().withMessage("Invalid user ID"),
  body("planId").isMongoId().withMessage("Invalid plan ID"),
  body("paymentMethod").notEmpty().withMessage("Payment method is required"),
];
