import { body, validationResult } from "express-validator";

export const validateCategory = [
  // Name validation
  body("name")
    .notEmpty()
    .withMessage("Category name is required.")
    .isString()
    .withMessage("Category name must be a string."),

  // Slug validation
  body("slug").optional().isString().withMessage("Slug must be a string."),

  // Description validation
  body("description")
    .optional()
    .isString()
    .withMessage("Description must be a string."),

  // parent validation
  body("parent")
    .optional()
    .custom((value) => {
      if (value === "") {
        // Allow empty string by treating it as valid (or transforming it into null later)
        return true;
      }
      if (!/^[a-fA-F0-9]{24}$/.test(value)) {
        throw new Error("Parent category must be a valid MongoDB ObjectId.");
      }
      return true;
    }),

  // Validate and handle errors
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array(),
      });
    }
    // Transform parent if empty string
    if (req.body.parent === "") {
      req.body.parent = null;
    }
    next();
  },
];
