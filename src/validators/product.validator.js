import { body } from "express-validator";
import { PRODUCT_TYPES } from "../utils/constants.js";
import { validateRequest } from "../middlewares/validateRequest.middleware.js";

export const validateProduct = [
  body("name").notEmpty().withMessage("Name is required."),

  body("description")
    .optional()
    .isString()
    .withMessage("Description must be a string."),

  body("type")
    .notEmpty()
    .withMessage("Product type is required.")
    .isIn(Object.values(PRODUCT_TYPES))
    .withMessage(
      `Type must be one of: ${Object.values(PRODUCT_TYPES).join(", ")}`
    ),

  body("subTypes")
    .optional()
    .isArray()
    .withMessage("subTypes must be an array of strings.")
    .custom((subTypes) => subTypes.every((id) => typeof id === "string"))
    .withMessage("Each subcategory ID must be a valid string."),

  body("tags")
    .optional()
    .isArray()
    .withMessage("Tags must be an array of strings.")
    .custom((tags) => tags.every((tag) => typeof tag === "string"))
    .withMessage("Each tag must be a string."),

  body("techStack")
    .optional()
    .isArray()
    .withMessage("Tech stack must be an array of ObjectIds.")
    .custom((techStack) => techStack.every((item) => typeof item === "string" && item.match(/^[0-9a-fA-F]{24}$/)))
    .withMessage("Each tech stack item must be a valid ObjectId."),

  body("price").optional().isNumeric().withMessage("Price must be a number."),

  body("isPaid")
    .optional()
    .isBoolean()
    .withMessage("isPaid must be a boolean."),

  body("downloads")
    .optional()
    .isInt({ min: 0 })
    .withMessage("Downloads must be a non-negative integer."),

  body("previewUrl")
    .optional()
    .isURL()
    .withMessage("Preview URL must be a valid URL."),

  body("paymentLink")
    .optional()
    .isURL()
    .withMessage("Payment link must be a valid URL."),

  // body("authorId")
  //   .notEmpty()
  //   .withMessage("Author ID is required.")
  //   .isString()
  //   .withMessage("Author ID must be a valid string."),

  body("rating")
    .optional()
    .isFloat({ min: 0, max: 5 })
    .withMessage("Rating must be a number between 0 and 5."),

  // body("keyFeatures")
  //   .optional()
  //   .isArray()
  //   .withMessage("Key features must be an array of strings.")
  //   .custom((features) =>
  //     features.every((feature) => typeof feature === "string")
  //   )
  //   .withMessage("Each key feature must be a string."),

  // body("highlights")
  //   .optional()
  //   .isArray()
  //   .withMessage("Highlights must be an array of strings.")
  //   .custom((highlights) =>
  //     highlights.every((highlight) => typeof highlight === "string")
  //   )
  //   .withMessage("Each highlight must be a string."),

  body("keyFeatures")
    .optional()
    .custom((value) => {
      if (typeof value === 'string') {
        try {
          JSON.parse(value);
          return true;
        } catch (e) {
          return false;
        }
      }
      return typeof value === 'object';
    })
    .withMessage("Key features must be a valid JSON object or string."),

  body("highlights")
    .optional()
    .custom((value) => {
      if (typeof value === 'string') {
        try {
          JSON.parse(value);
          return true;
        } catch (e) {
          return false;
        }
      }
      return typeof value === 'object';
    })
    .withMessage("Highlights must be a valid JSON object or string."),

  body("status")
    .optional()
    .isIn(["active", "inactive", "archived"])
    .withMessage('Status must be one of: "active", "inactive", "archived".'),

  body("isFeatured")
    .optional()
    .isBoolean()
    .withMessage("isFeatured must be a boolean."),

  body("discount")
    .optional()
    .isFloat({ min: 0 })
    .withMessage("Discount must be a non-negative number."),

  // Call validateRequest to process errors after validation
  validateRequest,
];
