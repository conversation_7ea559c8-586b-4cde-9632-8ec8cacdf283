// authMiddleware.js
import jwt from "jsonwebtoken";
import User from "../models/user.model.js";

// JWT-based authentication middleware
export const protect = async (req, res, next) => {
  try {
    let token;

    // Check for token in Authorization header
    if (req.headers.authorization && req.headers.authorization.startsWith("Bearer")) {
      token = req.headers.authorization.split(" ")[1];
    }
    // Check for token in cookies
    else if (req.cookies.token) {
      token = req.cookies.token;
    }

    if (!token) {
      return res.status(401).json({
        success: false,
        message: "Access denied. No token provided."
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Get user from database
    const user = await User.findById(decoded.id).select("-password");

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "Token is not valid. User not found."
      });
    }

    if (user.isDeleted) {
      return res.status(401).json({
        success: false,
        message: "Account has been deactivated."
      });
    }

    // Add user to request object
    req.user = user;
    next();
  } catch (error) {
    console.error("Auth middleware error:", error);
    return res.status(401).json({
      success: false,
      message: "Token is not valid."
    });
  }
};

// Check for permission based on roles
export const authorize = (roles) => {
  return async (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: "Access denied. Please authenticate first."
      });
    }

    // it means if the roles array does not include the user role
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: "Access denied. Insufficient permissions."
      });
    }

    next();
  };
};

//export const requireSession = async (req, res, next) => {
//   try {
//     const { sessionId } = req.headers; // Assuming sessionId is sent in the request headers

//     if (!sessionId) {
//       return res.status(400).json({ message: "Session ID is required" });
//     }

//     // Verify the session using Clerk's SDK
//     const session = await Clerk.sessions.verifySession(sessionId);

//     if (!session) {
//       return res.status(401).json({ message: "Invalid session" });
//     }

//     // Attach session data to request for use in other parts of the app
//     req.session = session;

//     next();
//   } catch (error) {
//     console.error("Session verification error:", error);
//     return res.status(500).json({ message: "Session verification failed" });
//   }
// };

//not required while using clerk as clerk provide requireAuth() function to check if user is authenticated or not

// export const protect = async (req, res, next) => {
//   const token = req.header("Authorization")?.split(" ")[1];

//   if (!token) {
//     return res.status(401).json({ message: "No token, authorization denied" });
//   }

//   try {
//     const decoded = jwt.verify(token, process.env.JWT_SECRET);
//     req.user = await User.findById(decoded.id);
//     next();
//   } catch (err) {
//     res.status(401).json({ message: "Token is not valid" });
//   }
// };
