/**
 * Subscription System Usage Examples
 * 
 * This file demonstrates how to use the subscription system
 * with different payment gateways and scenarios.
 */

import { subscriptionService } from '../src/services/subscription/SubscriptionService.js';
import { webhookRegistry } from '../src/services/webhooks/index.js';
import User from '../src/models/user.model.js';
import Plan from '../src/models/plan.model.js';

// Example 1: Processing a Razorpay subscription webhook
async function handleRazorpaySubscriptionWebhook(req, res) {
  try {
    const payload = req.body;
    const signature = req.headers['x-razorpay-signature'];
    
    // Get Razorpay handler
    const handler = webhookRegistry.getHandler('razorpay');
    
    // Process webhook
    const webhookData = await handler.processWebhook(payload, signature);
    
    if (!webhookData.success) {
      return res.status(400).json({ error: webhookData.error });
    }
    
    // Handle subscription logic
    const result = await subscriptionService.handleSubscriptionWebhook(webhookData);
    
    res.status(200).json({
      success: true,
      message: 'Webhook processed successfully',
      result
    });
    
  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}

// Example 2: Creating a subscription plan
async function createSubscriptionPlan() {
  const plan = new Plan({
    name: "Pro Monthly",
    description: "Monthly pro subscription with premium features",
    price: 29.99,
    currency: "USD",
    duration: "monthly",
    benefits: "Access to premium templates, priority support, advanced features",
    isActive: true,
    maxTemplates: 100,
    maxUsers: 5
  });
  
  await plan.save();
  console.log('Plan created:', plan);
  return plan;
}

// Example 3: Manually activating a user subscription (for testing)
async function activateUserSubscription(userEmail, planId) {
  try {
    const user = await User.findOne({ email: userEmail });
    const plan = await Plan.findById(planId);
    
    if (!user || !plan) {
      throw new Error('User or plan not found');
    }
    
    // Activate subscription
    await user.activateSubscription(plan, null);
    
    console.log('Subscription activated:', {
      user: user.email,
      plan: plan.name,
      tier: user.subscriptionTier,
      status: user.subscriptionStatus,
      endDate: user.subscriptionEndDate
    });
    
    return user;
  } catch (error) {
    console.error('Subscription activation error:', error);
    throw error;
  }
}

// Example 4: Checking user subscription status
async function checkUserSubscription(userEmail) {
  try {
    const user = await User.findOne({ email: userEmail });
    
    if (!user) {
      throw new Error('User not found');
    }
    
    const subscriptionSummary = user.getSubscriptionSummary();
    
    console.log('Subscription Summary:', subscriptionSummary);
    
    // Check specific permissions
    console.log('Can download:', user.canDownload());
    console.log('Has active subscription:', user.hasActiveSubscription());
    console.log('Is lifetime user:', user.isLifetimePro);
    
    return subscriptionSummary;
  } catch (error) {
    console.error('Error checking subscription:', error);
    throw error;
  }
}

// Example 5: Handling subscription cancellation
async function cancelUserSubscription(userEmail, reason = 'User requested') {
  try {
    const user = await User.findOne({ email: userEmail });
    
    if (!user) {
      throw new Error('User not found');
    }
    
    await user.cancelSubscription(null, reason);
    
    console.log('Subscription cancelled:', {
      user: user.email,
      reason: reason,
      effectiveDate: user.cancellation?.effectiveDate,
      status: user.subscriptionStatus
    });
    
    return user;
  } catch (error) {
    console.error('Cancellation error:', error);
    throw error;
  }
}

// Example 6: Creating a test webhook payload for Razorpay
function createTestRazorpayWebhook(eventType = 'subscription.activated') {
  const basePayload = {
    event: eventType,
    payload: {
      subscription: {
        id: `sub_test_${Date.now()}`,
        status: 'active',
        plan_id: 'plan_test123',
        customer_id: `cust_test_${Date.now()}`,
        start_at: Math.floor(Date.now() / 1000),
        end_at: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000),
        current_start: Math.floor(Date.now() / 1000),
        current_end: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000),
        plan: {
          item: {
            name: "Pro Monthly",
            amount: 2999, // in paise
            currency: "INR"
          }
        },
        notes: {
          email: "<EMAIL>",
          customer_name: "Test User"
        }
      }
    }
  };
  
  // Customize payload based on event type
  switch (eventType) {
    case 'payment.captured':
      basePayload.payload = {
        payment: {
          id: `pay_test_${Date.now()}`,
          amount: 2999,
          currency: "INR",
          status: "captured",
          method: "card",
          email: "<EMAIL>"
        }
      };
      break;
      
    case 'subscription.cancelled':
      basePayload.payload.subscription.status = 'cancelled';
      break;
  }
  
  return basePayload;
}

// Example 7: Testing webhook processing
async function testWebhookProcessing() {
  try {
    // Create test plan
    const plan = await createSubscriptionPlan();
    
    // Create test webhook payload
    const webhookPayload = createTestRazorpayWebhook('subscription.activated');
    
    // Get handler and process webhook
    const handler = webhookRegistry.getHandler('razorpay');
    
    // Mock signature verification for testing
    handler.verifySignature = () => true;
    
    const webhookData = await handler.processWebhook(webhookPayload, 'test-signature');
    
    if (webhookData.success) {
      const result = await subscriptionService.handleSubscriptionWebhook(webhookData);
      console.log('Test webhook processing result:', result);
    } else {
      console.error('Webhook processing failed:', webhookData.error);
    }
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

// Example 8: Middleware for protecting premium routes
function requireActiveSubscription(req, res, next) {
  const user = req.user; // Assuming user is attached to request
  
  if (!user) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  
  if (!user.hasActiveSubscription() && !user.isLifetimePro) {
    return res.status(403).json({ 
      error: 'Active subscription required',
      subscriptionStatus: user.subscriptionStatus,
      upgradeUrl: '/pricing'
    });
  }
  
  next();
}

// Example 9: Download limit middleware
function checkDownloadLimit(req, res, next) {
  const user = req.user;
  
  if (!user) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  
  if (!user.canDownload()) {
    return res.status(429).json({
      error: 'Download limit exceeded',
      limit: user.monthlyDownloadLimit,
      current: user.downloadCount,
      upgradeUrl: '/pricing'
    });
  }
  
  next();
}

// Example 10: Usage in Express routes
function setupSubscriptionRoutes(app) {
  // Webhook endpoints
  app.post('/api/webhooks/razorpay', handleRazorpaySubscriptionWebhook);
  
  // Protected routes
  app.get('/api/premium-content', requireActiveSubscription, (req, res) => {
    res.json({ message: 'This is premium content!' });
  });
  
  app.post('/api/download', checkDownloadLimit, async (req, res) => {
    // Increment download count
    await req.user.incrementDownload();
    res.json({ message: 'Download started' });
  });
  
  // Subscription management
  app.get('/api/user/subscription', async (req, res) => {
    const summary = req.user.getSubscriptionSummary();
    res.json(summary);
  });
  
  app.post('/api/user/subscription/cancel', async (req, res) => {
    await req.user.cancelSubscription(req.user._id, 'User requested');
    res.json({ message: 'Subscription cancelled successfully' });
  });
}

export {
  handleRazorpaySubscriptionWebhook,
  createSubscriptionPlan,
  activateUserSubscription,
  checkUserSubscription,
  cancelUserSubscription,
  createTestRazorpayWebhook,
  testWebhookProcessing,
  requireActiveSubscription,
  checkDownloadLimit,
  setupSubscriptionRoutes
};
