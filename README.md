
```markdown
# DesignByte Server

## Overview

DesignByte is a comprehensive platform for selling developer resources including starter kits, templates, and projects through a subscription model. The platform supports free, monthly, yearly, and lifetime subscription tiers with secure access control for paid content.

## Directory Structure

The project follows the structure outlined below:

```
project-root /
│
├── package.json          # Project metadata and dependencies
├── package-lock.json     # Auto-generated dependency tree
├── node_modules/         # Installed npm packages
├── .env                  # Environment variables
├── .gitignore            # Git ignore rules
│
├── src/                  # Application source code
│   ├── app.js            # Entry point for the Express app
│   ├── config/           # Configuration files
│   │   ├── db.js         # Database connection setup
│   │   ├── passport.js   # Passport authentication configuration
│   │
│   ├── controllers/      # Business logic for routes
│   │   ├── auth.controller.js
│   │   ├── user.controller.js
│   │   ├── template.controller.js
│   │   ├── category.controller.js
│   │   ├── coupon.controller.js
│   │   ├── fileSystem.controller.js
│   │   ├── transaction.controller.js
│   │
│   ├── middlewares/      # Middleware functions
│   │   ├── auth.middleware.js  # JWT auth verification and authorization
│   │   ├── error.middleware.js # Global error handler
│   │
│   ├── models/           # Database models
│   │   ├── user.model.js
│   │   ├── template.model.js
│   │   ├── category.model.js
│   │   ├── coupon.model.js
│   │   ├── transaction.model.js
│   │   ├── plan.model.js
│   │   ├── session.model.js
│   │   ├── folder.model.js (unified file system)
│   │
│   ├── routes/           # Route definitions
│   │   ├── auth.routes.js
│   │   ├── user.routes.js
│   │   ├── template.routes.js
│   │   ├── category.routes.js
│   │   ├── coupon.routes.js
│   │   ├── subscription.routes.js
│   │   ├── transaction.routes.js
│   │
│   ├── services/         # Reusable services (business logic helpers)
│   │   ├── payment.service.js   # Payment gateway integrations
│   │   ├── email.service.js     # Email notifications (Resend)
│   │   ├── subscription.service.js # Subscription handling
│   │   ├── transaction.service.js # Transaction processing
│   │
│   ├── utils/            # Utility functions
│   │   ├── logger.js         # Logging utilities
│   │   ├── validators.js     # Validation utilities
│   │   ├── constants.js      # App-wide constants
│   │
│   └── validators/       # Request validation schemas
│       ├── auth.validator.js
│       ├── template.validator.js
│       ├── coupon.validator.js
│
└── README.md             # Documentation
```

### Key Directories and Files

1. **`src/`**: Contains all application source code.
   - **`app.js`**: Main entry point to start the Express application.
   
2. **`config/`**: Configuration files for database and authentication.
   - **`db.js`**: Sets up the database connection.
   - **`passport.js`**: Passport authentication setup for Google OAuth and local strategy.

3. **`controllers/`**: Contains the business logic for the application routes.
   - Files like `auth.controller.js`, `template.controller.js`, etc., manage requests and responses for various features.

4. **`middlewares/`**: Middleware functions.
   - **`auth.middleware.js`**: JWT authentication and role-based authorization middleware.
   - **`error.middleware.js`**: Handles global errors.

5. **`models/`**: Mongoose models for database entities.
   - Models such as `user.model.js`, `template.model.js`, etc., define the structure and validation of data in MongoDB.

6. **`routes/`**: Defines routes for the application.
   - Includes routes for authentication, templates, categories, orders, etc.

7. **`services/`**: Contains reusable business logic helpers.
   - Includes services like payment integration and email notifications using Resend.

8. **`utils/`**: Utility functions and constants used across the app.
   - Includes helpers for logging, validation, and constants.

9. **`validators/`**: Validation schemas for request data.
   - Ensures that incoming requests follow the correct format before processing.

---

## Setup and Installation

1. Clone the repository:

   ```bash
   git clone <repository-url>
   cd <project-folder>
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Set up environment variables in `.env` file:

   Example `.env` file:

   ```
   # Database Configuration
   MONGODB_URI=mongodb://localhost:27017/designbyte
   NODE_ENV=development

   # JWT Configuration
   JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
   JWT_EXPIRES_IN=30d

   # Email Configuration (Resend)
   RESEND_API_KEY=re_your_resend_api_key_here
   FROM_NAME=DesignByte
   FROM_EMAIL=<EMAIL>

   # Google OAuth Configuration
   GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
   GOOGLE_CLIENT_SECRET=your-google-client-secret

   # Client URL
   CLIENT_URL=http://localhost:3000
   CORS_ORIGIN=http://localhost:3000

   # Upstash Redis Configuration
   UPSTASH_REDIS_REST_URL=https://your-redis-url.upstash.io
   UPSTASH_REDIS_REST_TOKEN=your-redis-token
   ```

4. Set up external services:

   **Resend Email Service:**
   - Go to [Resend Console](https://resend.com/)
   - Create an account and verify your domain
   - Generate an API key
   - Add the API key to your `.env` file as `RESEND_API_KEY`

   **Google OAuth (Optional):**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable Google+ API
   - Create OAuth 2.0 credentials
   - Add the credentials to your `.env` file

   **Upstash Redis:**
   - Go to [Upstash Console](https://console.upstash.com/)
   - Create a new Redis database
   - Copy the REST URL and token to your `.env` file

5. Run the application:

   ```bash
   npm start
   ```

## Authentication

The application uses a custom JWT-based authentication system with the following features:

- **Local Authentication**: Email/password registration and login
- **Google OAuth**: Social login integration
- **JWT Tokens**: Secure token-based authentication
- **Role-based Authorization**: Admin, super_admin, and user roles
- **Email Verification**: Account verification via email
- **Password Reset**: Secure password reset functionality

## Email Service

The application uses Resend for reliable email delivery:

- **Email Verification**: Sent during user registration
- **Password Reset**: Secure password reset emails
- **Welcome Emails**: Sent after email verification
- **Transactional Emails**: Order confirmations and notifications

---

## Contributing

Feel free to fork the repository and submit pull requests. Please follow the code style and ensure that tests are written for new features.

---

## License

Include license information here.

---
```

